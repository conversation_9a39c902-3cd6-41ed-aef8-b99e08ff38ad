import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_spacing.dart';
import '../../theme/app_typography.dart';
import '../../theme/app_theme_extensions.dart';

/// Luminar design system button component
/// 
/// A customizable button that follows Material 3 design principles
/// with Luminar-specific styling and animations.
class LuminarButton extends StatefulWidget {
  const LuminarButton({
    super.key,
    required this.onPressed,
    required this.child,
    this.variant = LuminarButtonVariant.primary,
    this.size = LuminarButtonSize.medium,
    this.isLoading = false,
    this.isDisabled = false,
    this.icon,
    this.iconPosition = LuminarButtonIconPosition.leading,
    this.fullWidth = false,
    this.borderRadius,
    this.elevation,
    this.animationDuration,
  });

  /// Callback when button is pressed
  final VoidCallback? onPressed;

  /// Button content (usually Text widget)
  final Widget child;

  /// Button style variant
  final LuminarButtonVariant variant;

  /// Button size
  final LuminarButtonSize size;

  /// Whether button is in loading state
  final bool isLoading;

  /// Whether button is disabled
  final bool isDisabled;

  /// Optional icon
  final IconData? icon;

  /// Icon position relative to text
  final LuminarButtonIconPosition iconPosition;

  /// Whether button should take full width
  final bool fullWidth;

  /// Custom border radius
  final BorderRadius? borderRadius;

  /// Custom elevation
  final double? elevation;

  /// Custom animation duration
  final Duration? animationDuration;

  @override
  State<LuminarButton> createState() => _LuminarButtonState();
}

class _LuminarButtonState extends State<LuminarButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationDuration ?? const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (!_isDisabled) {
      setState(() => _isPressed = true);
      _animationController.forward();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (!_isDisabled) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  void _handleTapCancel() {
    if (!_isDisabled) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  bool get _isDisabled => widget.isDisabled || widget.onPressed == null;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final shadowTheme = theme.extension<ShadowTheme>();

    // Get button configuration based on variant and size
    final config = _getButtonConfig(colorScheme);
    final sizeConfig = _getSizeConfig();

    Widget buttonChild = _buildButtonContent(config);

    if (widget.fullWidth) {
      buttonChild = SizedBox(
        width: double.infinity,
        child: buttonChild,
      );
    }

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: _handleTapDown,
            onTapUp: _handleTapUp,
            onTapCancel: _handleTapCancel,
            onTap: _isDisabled ? null : widget.onPressed,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              decoration: BoxDecoration(
                color: config.backgroundColor,
                borderRadius: widget.borderRadius ?? BorderRadius.circular(8),
                border: config.borderColor != null
                    ? Border.all(color: config.borderColor!)
                    : null,
                boxShadow: widget.elevation != null
                    ? shadowTheme?.elevation2
                    : config.elevation > 0
                        ? shadowTheme?.elevation1
                        : null,
              ),
              padding: sizeConfig.padding,
              child: buttonChild,
            ),
          ),
        );
      },
    );
  }

  Widget _buildButtonContent(_ButtonConfig config) {
    final children = <Widget>[];

    // Add loading indicator or icon
    if (widget.isLoading) {
      children.add(
        SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(config.foregroundColor),
          ),
        ),
      );
      if (widget.icon != null || children.isNotEmpty) {
        children.add(AppSpacing.horizontalGapSM);
      }
    } else if (widget.icon != null) {
      final iconWidget = Icon(
        widget.icon,
        color: config.foregroundColor,
        size: _getSizeConfig().iconSize,
      );

      if (widget.iconPosition == LuminarButtonIconPosition.leading) {
        children.add(iconWidget);
        children.add(AppSpacing.horizontalGapSM);
      } else {
        children.add(AppSpacing.horizontalGapSM);
        children.add(iconWidget);
      }
    }

    // Add main content
    final contentIndex = widget.iconPosition == LuminarButtonIconPosition.leading
        ? children.length
        : 0;

    children.insert(
      contentIndex,
      DefaultTextStyle(
        style: _getSizeConfig().textStyle.copyWith(
          color: config.foregroundColor,
        ),
        child: widget.child,
      ),
    );

    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: children,
    );
  }

  _ButtonConfig _getButtonConfig(ColorScheme colorScheme) {
    switch (widget.variant) {
      case LuminarButtonVariant.primary:
        return _ButtonConfig(
          backgroundColor: _isDisabled
              ? colorScheme.onSurface.withOpacity(0.12)
              : colorScheme.primary,
          foregroundColor: _isDisabled
              ? colorScheme.onSurface.withOpacity(0.38)
              : colorScheme.onPrimary,
          elevation: 2,
        );
      case LuminarButtonVariant.secondary:
        return _ButtonConfig(
          backgroundColor: _isDisabled
              ? colorScheme.onSurface.withOpacity(0.12)
              : colorScheme.secondaryContainer,
          foregroundColor: _isDisabled
              ? colorScheme.onSurface.withOpacity(0.38)
              : colorScheme.onSecondaryContainer,
          elevation: 1,
        );
      case LuminarButtonVariant.outline:
        return _ButtonConfig(
          backgroundColor: Colors.transparent,
          foregroundColor: _isDisabled
              ? colorScheme.onSurface.withOpacity(0.38)
              : colorScheme.primary,
          borderColor: _isDisabled
              ? colorScheme.onSurface.withOpacity(0.12)
              : colorScheme.outline,
          elevation: 0,
        );
      case LuminarButtonVariant.text:
        return _ButtonConfig(
          backgroundColor: Colors.transparent,
          foregroundColor: _isDisabled
              ? colorScheme.onSurface.withOpacity(0.38)
              : colorScheme.primary,
          elevation: 0,
        );
    }
  }

  _SizeConfig _getSizeConfig() {
    switch (widget.size) {
      case LuminarButtonSize.small:
        return _SizeConfig(
          padding: AppSpacing.paddingMD,
          textStyle: AppTypography.labelMedium,
          iconSize: 16,
        );
      case LuminarButtonSize.medium:
        return _SizeConfig(
          padding: AppSpacing.paddingLG,
          textStyle: AppTypography.labelLarge,
          iconSize: 18,
        );
      case LuminarButtonSize.large:
        return _SizeConfig(
          padding: AppSpacing.paddingXL,
          textStyle: AppTypography.titleMedium,
          iconSize: 20,
        );
    }
  }
}

/// Button variant styles
enum LuminarButtonVariant {
  primary,
  secondary,
  outline,
  text,
}

/// Button sizes
enum LuminarButtonSize {
  small,
  medium,
  large,
}

/// Icon position relative to text
enum LuminarButtonIconPosition {
  leading,
  trailing,
}

/// Internal button configuration
class _ButtonConfig {
  const _ButtonConfig({
    required this.backgroundColor,
    required this.foregroundColor,
    required this.elevation,
    this.borderColor,
  });

  final Color backgroundColor;
  final Color foregroundColor;
  final Color? borderColor;
  final double elevation;
}

/// Internal size configuration
class _SizeConfig {
  const _SizeConfig({
    required this.padding,
    required this.textStyle,
    required this.iconSize,
  });

  final EdgeInsets padding;
  final TextStyle textStyle;
  final double iconSize;
}
