// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in brainstorm_flutter/test/widget/settings_page_widget_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;
import 'dart:ui' as _i8;

import 'package:brainstorm_flutter/core/ollama_api/model_manager.dart' as _i7;
import 'package:brainstorm_flutter/core/ollama_api/monitoring/error_tracker.dart'
    as _i3;
import 'package:brainstorm_flutter/core/ollama_api/monitoring/health_monitor.dart'
    as _i4;
import 'package:brainstorm_flutter/core/ollama_api/ollama_api_manager.dart'
    as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i5;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeDuration_0 extends _i1.SmartFake implements Duration {
  _FakeDuration_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeOllamaResponse_1 extends _i1.SmartFake
    implements _i2.OllamaResponse {
  _FakeOllamaResponse_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeOllamaInstallationStatus_2 extends _i1.SmartFake
    implements _i2.OllamaInstallationStatus {
  _FakeOllamaInstallationStatus_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeErrorStats_3 extends _i1.SmartFake implements _i3.ErrorStats {
  _FakeErrorStats_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeHealthCheckResult_4 extends _i1.SmartFake
    implements _i4.HealthCheckResult {
  _FakeHealthCheckResult_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [OllamaApiManager].
///
/// See the documentation for Mockito's code generation for more information.
class MockOllamaApiManager extends _i1.Mock implements _i2.OllamaApiManager {
  MockOllamaApiManager() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get baseUrl =>
      (super.noSuchMethod(
            Invocation.getter(#baseUrl),
            returnValue: _i5.dummyValue<String>(
              this,
              Invocation.getter(#baseUrl),
            ),
          )
          as String);

  @override
  Duration get timeout =>
      (super.noSuchMethod(
            Invocation.getter(#timeout),
            returnValue: _FakeDuration_0(this, Invocation.getter(#timeout)),
          )
          as Duration);

  @override
  int get maxRetries =>
      (super.noSuchMethod(Invocation.getter(#maxRetries), returnValue: 0)
          as int);

  @override
  Duration get retryDelay =>
      (super.noSuchMethod(
            Invocation.getter(#retryDelay),
            returnValue: _FakeDuration_0(this, Invocation.getter(#retryDelay)),
          )
          as Duration);

  @override
  _i2.OllamaConnectionStatus get connectionStatus =>
      (super.noSuchMethod(
            Invocation.getter(#connectionStatus),
            returnValue: _i2.OllamaConnectionStatus.connected,
          )
          as _i2.OllamaConnectionStatus);

  @override
  bool get shouldRetryConnection =>
      (super.noSuchMethod(
            Invocation.getter(#shouldRetryConnection),
            returnValue: false,
          )
          as bool);

  @override
  _i6.Future<bool> isOllamaAvailable() =>
      (super.noSuchMethod(
            Invocation.method(#isOllamaAvailable, []),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<String?> getOllamaVersion() =>
      (super.noSuchMethod(
            Invocation.method(#getOllamaVersion, []),
            returnValue: _i6.Future<String?>.value(),
          )
          as _i6.Future<String?>);

  @override
  _i6.Future<List<_i2.OllamaModel>> getAvailableModels() =>
      (super.noSuchMethod(
            Invocation.method(#getAvailableModels, []),
            returnValue: _i6.Future<List<_i2.OllamaModel>>.value(
              <_i2.OllamaModel>[],
            ),
          )
          as _i6.Future<List<_i2.OllamaModel>>);

  @override
  _i6.Future<_i2.OllamaResponse> sendChatRequest({
    required String? model,
    required String? prompt,
    String? sessionId,
    List<String>? contextFiles,
    Map<String, String>? environment,
    bool? stream = false,
    bool? enableCaching = true,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendChatRequest, [], {
              #model: model,
              #prompt: prompt,
              #sessionId: sessionId,
              #contextFiles: contextFiles,
              #environment: environment,
              #stream: stream,
              #enableCaching: enableCaching,
            }),
            returnValue: _i6.Future<_i2.OllamaResponse>.value(
              _FakeOllamaResponse_1(
                this,
                Invocation.method(#sendChatRequest, [], {
                  #model: model,
                  #prompt: prompt,
                  #sessionId: sessionId,
                  #contextFiles: contextFiles,
                  #environment: environment,
                  #stream: stream,
                  #enableCaching: enableCaching,
                }),
              ),
            ),
          )
          as _i6.Future<_i2.OllamaResponse>);

  @override
  _i6.Future<_i2.OllamaResponse> sendToClaudeCLI({
    required String? prompt,
    String? sessionId,
    List<String>? contextFiles,
    Map<String, String>? environment,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendToClaudeCLI, [], {
              #prompt: prompt,
              #sessionId: sessionId,
              #contextFiles: contextFiles,
              #environment: environment,
            }),
            returnValue: _i6.Future<_i2.OllamaResponse>.value(
              _FakeOllamaResponse_1(
                this,
                Invocation.method(#sendToClaudeCLI, [], {
                  #prompt: prompt,
                  #sessionId: sessionId,
                  #contextFiles: contextFiles,
                  #environment: environment,
                }),
              ),
            ),
          )
          as _i6.Future<_i2.OllamaResponse>);

  @override
  _i6.Future<bool> isClaudeAvailable() =>
      (super.noSuchMethod(
            Invocation.method(#isClaudeAvailable, []),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<String?> getClaudeVersion() =>
      (super.noSuchMethod(
            Invocation.method(#getClaudeVersion, []),
            returnValue: _i6.Future<String?>.value(),
          )
          as _i6.Future<String?>);

  @override
  _i6.Future<String?> startInteractiveSession({
    required String? sessionId,
    List<String>? contextFiles,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#startInteractiveSession, [], {
              #sessionId: sessionId,
              #contextFiles: contextFiles,
            }),
            returnValue: _i6.Future<String?>.value(),
          )
          as _i6.Future<String?>);

  @override
  _i6.Future<bool> sendToInteractiveSession(
    String? sessionId,
    String? message,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#sendToInteractiveSession, [sessionId, message]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<void> closeSession(String? sessionId) =>
      (super.noSuchMethod(
            Invocation.method(#closeSession, [sessionId]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> closeAllSessions() =>
      (super.noSuchMethod(
            Invocation.method(#closeAllSessions, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  List<_i2.OllamaMessage> getSessionHistory(String? sessionId) =>
      (super.noSuchMethod(
            Invocation.method(#getSessionHistory, [sessionId]),
            returnValue: <_i2.OllamaMessage>[],
          )
          as List<_i2.OllamaMessage>);

  @override
  _i6.Future<bool> isModelAvailable(String? modelName) =>
      (super.noSuchMethod(
            Invocation.method(#isModelAvailable, [modelName]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<List<_i2.OllamaModel>> getRecommendedModels() =>
      (super.noSuchMethod(
            Invocation.method(#getRecommendedModels, []),
            returnValue: _i6.Future<List<_i2.OllamaModel>>.value(
              <_i2.OllamaModel>[],
            ),
          )
          as _i6.Future<List<_i2.OllamaModel>>);

  @override
  _i6.Future<bool> pullModel(String? modelName) =>
      (super.noSuchMethod(
            Invocation.method(#pullModel, [modelName]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<Map<String, dynamic>?> getServerInfo() =>
      (super.noSuchMethod(
            Invocation.method(#getServerInfo, []),
            returnValue: _i6.Future<Map<String, dynamic>?>.value(),
          )
          as _i6.Future<Map<String, dynamic>?>);

  @override
  _i6.Future<_i2.OllamaInstallationStatus> getInstallationStatus() =>
      (super.noSuchMethod(
            Invocation.method(#getInstallationStatus, []),
            returnValue: _i6.Future<_i2.OllamaInstallationStatus>.value(
              _FakeOllamaInstallationStatus_2(
                this,
                Invocation.method(#getInstallationStatus, []),
              ),
            ),
          )
          as _i6.Future<_i2.OllamaInstallationStatus>);

  @override
  _i6.Future<Map<String, dynamic>> getPerformanceStats() =>
      (super.noSuchMethod(
            Invocation.method(#getPerformanceStats, []),
            returnValue: _i6.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i6.Future<Map<String, dynamic>>);

  @override
  _i6.Future<void> clearCache() =>
      (super.noSuchMethod(
            Invocation.method(#clearCache, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> cleanExpiredCache() =>
      (super.noSuchMethod(
            Invocation.method(#cleanExpiredCache, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [OllamaModelManager].
///
/// See the documentation for Mockito's code generation for more information.
class MockOllamaModelManager extends _i1.Mock
    implements _i7.OllamaModelManager {
  MockOllamaModelManager() {
    _i1.throwOnMissingStub(this);
  }

  @override
  List<_i2.OllamaModel> get availableModels =>
      (super.noSuchMethod(
            Invocation.getter(#availableModels),
            returnValue: <_i2.OllamaModel>[],
          )
          as List<_i2.OllamaModel>);

  @override
  List<_i2.OllamaModel> get recommendedModels =>
      (super.noSuchMethod(
            Invocation.getter(#recommendedModels),
            returnValue: <_i2.OllamaModel>[],
          )
          as List<_i2.OllamaModel>);

  @override
  bool get isLoading =>
      (super.noSuchMethod(Invocation.getter(#isLoading), returnValue: false)
          as bool);

  @override
  bool get hasModels =>
      (super.noSuchMethod(Invocation.getter(#hasModels), returnValue: false)
          as bool);

  @override
  bool get hasRecommendedModels =>
      (super.noSuchMethod(
            Invocation.getter(#hasRecommendedModels),
            returnValue: false,
          )
          as bool);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(Invocation.getter(#hasListeners), returnValue: false)
          as bool);

  @override
  _i6.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> refreshModels() =>
      (super.noSuchMethod(
            Invocation.method(#refreshModels, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<bool> selectModel(String? modelName) =>
      (super.noSuchMethod(
            Invocation.method(#selectModel, [modelName]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<bool> autoSelectModel() =>
      (super.noSuchMethod(
            Invocation.method(#autoSelectModel, []),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<bool> isModelReady(String? modelName) =>
      (super.noSuchMethod(
            Invocation.method(#isModelReady, [modelName]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<bool> pullModel(String? modelName) =>
      (super.noSuchMethod(
            Invocation.method(#pullModel, [modelName]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  List<_i2.OllamaModel> getModelsForUseCase(String? useCase) =>
      (super.noSuchMethod(
            Invocation.method(#getModelsForUseCase, [useCase]),
            returnValue: <_i2.OllamaModel>[],
          )
          as List<_i2.OllamaModel>);

  @override
  String getCurrentModelName() =>
      (super.noSuchMethod(
            Invocation.method(#getCurrentModelName, []),
            returnValue: _i5.dummyValue<String>(
              this,
              Invocation.method(#getCurrentModelName, []),
            ),
          )
          as String);

  @override
  Map<String, dynamic> getModelStats() =>
      (super.noSuchMethod(
            Invocation.method(#getModelStats, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  void debouncedRefresh() => super.noSuchMethod(
    Invocation.method(#debouncedRefresh, []),
    returnValueForMissingStub: null,
  );

  @override
  _i6.Future<void> clearCache() =>
      (super.noSuchMethod(
            Invocation.method(#clearCache, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  void addListener(_i8.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#addListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void removeListener(_i8.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyListeners() => super.noSuchMethod(
    Invocation.method(#notifyListeners, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [OllamaErrorTracker].
///
/// See the documentation for Mockito's code generation for more information.
class MockOllamaErrorTracker extends _i1.Mock
    implements _i3.OllamaErrorTracker {
  MockOllamaErrorTracker() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Stream<_i3.ErrorEvent> get errorStream =>
      (super.noSuchMethod(
            Invocation.getter(#errorStream),
            returnValue: _i6.Stream<_i3.ErrorEvent>.empty(),
          )
          as _i6.Stream<_i3.ErrorEvent>);

  @override
  _i6.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> trackError(_i3.ErrorEvent? error) =>
      (super.noSuchMethod(
            Invocation.method(#trackError, [error]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> trackException(
    Exception? exception, {
    String? operation,
    Map<String, dynamic>? context = const {},
    Duration? duration,
    StackTrace? stackTrace,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #trackException,
              [exception],
              {
                #operation: operation,
                #context: context,
                #duration: duration,
                #stackTrace: stackTrace,
              },
            ),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<_i3.ErrorStats> getErrorStats({Duration? period}) =>
      (super.noSuchMethod(
            Invocation.method(#getErrorStats, [], {#period: period}),
            returnValue: _i6.Future<_i3.ErrorStats>.value(
              _FakeErrorStats_3(
                this,
                Invocation.method(#getErrorStats, [], {#period: period}),
              ),
            ),
          )
          as _i6.Future<_i3.ErrorStats>);

  @override
  List<_i3.ErrorEvent> getRecentErrors({
    int? limit,
    _i3.ErrorSeverity? minSeverity,
    _i3.ErrorCategory? category,
    Duration? since,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getRecentErrors, [], {
              #limit: limit,
              #minSeverity: minSeverity,
              #category: category,
              #since: since,
            }),
            returnValue: <_i3.ErrorEvent>[],
          )
          as List<_i3.ErrorEvent>);

  @override
  _i6.Future<void> clearErrors() =>
      (super.noSuchMethod(
            Invocation.method(#clearErrors, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<bool> isSystemHealthy() =>
      (super.noSuchMethod(
            Invocation.method(#isSystemHealthy, []),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<Map<String, dynamic>> getHealthStatus() =>
      (super.noSuchMethod(
            Invocation.method(#getHealthStatus, []),
            returnValue: _i6.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i6.Future<Map<String, dynamic>>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [OllamaHealthMonitor].
///
/// See the documentation for Mockito's code generation for more information.
class MockOllamaHealthMonitor extends _i1.Mock
    implements _i4.OllamaHealthMonitor {
  MockOllamaHealthMonitor() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Stream<_i4.HealthCheckResult> get healthStream =>
      (super.noSuchMethod(
            Invocation.getter(#healthStream),
            returnValue: _i6.Stream<_i4.HealthCheckResult>.empty(),
          )
          as _i6.Stream<_i4.HealthCheckResult>);

  @override
  List<_i4.HealthCheckResult> get healthHistory =>
      (super.noSuchMethod(
            Invocation.getter(#healthHistory),
            returnValue: <_i4.HealthCheckResult>[],
          )
          as List<_i4.HealthCheckResult>);

  @override
  void startMonitoring() => super.noSuchMethod(
    Invocation.method(#startMonitoring, []),
    returnValueForMissingStub: null,
  );

  @override
  void stopMonitoring() => super.noSuchMethod(
    Invocation.method(#stopMonitoring, []),
    returnValueForMissingStub: null,
  );

  @override
  _i6.Future<_i4.HealthCheckResult> performHealthCheck() =>
      (super.noSuchMethod(
            Invocation.method(#performHealthCheck, []),
            returnValue: _i6.Future<_i4.HealthCheckResult>.value(
              _FakeHealthCheckResult_4(
                this,
                Invocation.method(#performHealthCheck, []),
              ),
            ),
          )
          as _i6.Future<_i4.HealthCheckResult>);

  @override
  double getUptimePercentage({Duration? period}) =>
      (super.noSuchMethod(
            Invocation.method(#getUptimePercentage, [], {#period: period}),
            returnValue: 0.0,
          )
          as double);

  @override
  Duration getAverageResponseTime({Duration? period}) =>
      (super.noSuchMethod(
            Invocation.method(#getAverageResponseTime, [], {#period: period}),
            returnValue: _FakeDuration_0(
              this,
              Invocation.method(#getAverageResponseTime, [], {#period: period}),
            ),
          )
          as Duration);

  @override
  Map<String, dynamic> getHealthTrends({Duration? period}) =>
      (super.noSuchMethod(
            Invocation.method(#getHealthTrends, [], {#period: period}),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}
