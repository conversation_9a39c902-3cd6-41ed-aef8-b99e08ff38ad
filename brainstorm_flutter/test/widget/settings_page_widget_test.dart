import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:provider/provider.dart';
import '../../lib/core/ollama_api/ollama_api_manager.dart';
import '../../lib/core/ollama_api/model_manager.dart';
import '../../lib/core/ollama_api/monitoring/error_tracker.dart';
import '../../lib/core/ollama_api/monitoring/health_monitor.dart';
import '../../lib/features/settings/presentation/pages/settings_page.dart';
import '../../lib/features/settings/presentation/widgets/performance_stats_card.dart';
import '../../lib/features/settings/presentation/widgets/health_monitoring_card.dart';

// Generate mocks with: flutter packages pub run build_runner build
@GenerateMocks([
  OllamaApiManager,
  OllamaModelManager,
  OllamaErrorTracker,
  OllamaHealthMonitor,
])
import 'settings_page_widget_test.mocks.dart';

void main() {
  group('Settings Page Widget Tests', () {
    late MockOllamaApiManager mockApiManager;
    late MockOllamaModelManager mockModelManager;
    late MockOllamaErrorTracker mockErrorTracker;
    late MockOllamaHealthMonitor mockHealthMonitor;

    setUp(() {
      mockApiManager = MockOllamaApiManager();
      mockModelManager = MockOllamaModelManager();
      mockErrorTracker = MockOllamaErrorTracker();
      mockHealthMonitor = MockOllamaHealthMonitor();

      // Setup default mocks
      when(mockApiManager.getPerformanceStats()).thenAnswer((_) async => {
        'cache': {
          'memoryCacheSize': 10,
          'persistentCacheSize': 25,
        },
        'connectionPool': {
          'active_connections': 2,
          'idle_connections': 3,
          'max_connections': 5,
        },
        'sessionHistory': {
          'activeSessions': 1,
          'totalMessages': 15,
        },
        'throttler': {
          'canCall': true,
          'timeUntilNextCall': 0,
        },
      });

      when(mockHealthMonitor.currentHealth).thenReturn(
        HealthCheckResult(
          status: HealthStatus.healthy,
          timestamp: DateTime.now(),
          responseTime: const Duration(milliseconds: 150),
          metrics: {
            'connected': true,
            'available_models': 3,
            'error_rate': 0.5,
            'cache_size': 10,
            'active_connections': 2,
          },
          issues: [],
          statusMessage: 'All systems operational',
        ),
      );

      when(mockHealthMonitor.healthStream).thenAnswer((_) => const Stream.empty());
      when(mockErrorTracker.errorStream).thenAnswer((_) => const Stream.empty());
      when(mockErrorTracker.getErrorStats(period: anyNamed('period')))
          .thenAnswer((_) async => ErrorStats(
            totalErrors: 2,
            errorsBySeverity: {ErrorSeverity.low: 2},
            errorsByCategory: {ErrorCategory.cache: 2},
            errorsByOperation: {'cache_read': 2},
            errorRate: 0.5,
            avgResponseTime: const Duration(milliseconds: 100),
            topErrors: ['cache miss'],
          ));
      when(mockHealthMonitor.getHealthTrends(period: anyNamed('period')))
          .thenReturn({
            'trend': 'stable',
            'uptime': 99.5,
            'stability': 0.95,
            'avg_response_time': 150,
          });
    });

    Widget createTestWidget({Widget? child}) {
      return MaterialApp(
        home: MultiProvider(
          providers: [
            Provider<OllamaApiManager>.value(value: mockApiManager),
            Provider<OllamaModelManager>.value(value: mockModelManager),
            Provider<OllamaErrorTracker>.value(value: mockErrorTracker),
            Provider<OllamaHealthMonitor>.value(value: mockHealthMonitor),
          ],
          child: child ?? const SettingsPage(),
        ),
      );
    }

    group('Settings Page Tests', () {
      testWidgets('settings page loads and displays correctly', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Settings'), findsOneWidget);
        expect(find.byType(AppBar), findsOneWidget);
        expect(find.byType(ListView), findsOneWidget);
      });

      testWidgets('settings page contains performance stats card', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(PerformanceStatsCard), findsOneWidget);
        expect(find.text('Performance Statistics'), findsOneWidget);
        expect(find.text('Cache and connection pool metrics'), findsOneWidget);
      });

      testWidgets('settings page contains health monitoring card', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(HealthMonitoringCard), findsOneWidget);
        expect(find.text('System Health Monitor'), findsOneWidget);
        expect(find.text('All systems operational'), findsOneWidget);
      });

      testWidgets('settings page handles navigation correctly', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Find and tap back button
        final backButton = find.byType(BackButton);
        if (backButton.evaluate().isNotEmpty) {
          await tester.tap(backButton);
          await tester.pumpAndSettle();
        }

        // Assert - page should handle navigation (would need navigation testing in full app)
        expect(true, isTrue); // Placeholder
      });
    });

    group('Performance Stats Card Tests', () {
      testWidgets('performance stats card displays correctly', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget(
          child: Scaffold(
            body: PerformanceStatsCard(apiManager: mockApiManager),
          ),
        ));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Performance Statistics'), findsOneWidget);
        expect(find.text('Cache and connection pool metrics'), findsOneWidget);
        expect(find.byIcon(Icons.activity), findsOneWidget);
        expect(find.byIcon(Icons.refresh), findsOneWidget);
      });

      testWidgets('performance stats card loads and displays metrics', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget(
          child: Scaffold(
            body: PerformanceStatsCard(apiManager: mockApiManager),
          ),
        ));
        await tester.pumpAndSettle();

        // Assert metrics are displayed
        expect(find.text('Cache Performance'), findsOneWidget);
        expect(find.text('Connection Pool'), findsOneWidget);
        expect(find.text('Session Management'), findsOneWidget);
        expect(find.text('Request Throttling'), findsOneWidget);
      });

      testWidgets('performance stats card refresh button works', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget(
          child: Scaffold(
            body: PerformanceStatsCard(apiManager: mockApiManager),
          ),
        ));
        await tester.pumpAndSettle();

        // Tap refresh button
        await tester.tap(find.byIcon(Icons.refresh));
        await tester.pumpAndSettle();

        // Assert
        verify(mockApiManager.getPerformanceStats()).called(greaterThan(1));
      });

      testWidgets('performance stats card clear cache button works', (tester) async {
        // Arrange
        when(mockApiManager.clearCache()).thenAnswer((_) async {});

        // Act
        await tester.pumpWidget(createTestWidget(
          child: Scaffold(
            body: PerformanceStatsCard(apiManager: mockApiManager),
          ),
        ));
        await tester.pumpAndSettle();

        // Tap clear cache button
        await tester.tap(find.text('Clear Cache'));
        await tester.pumpAndSettle();

        // Assert
        verify(mockApiManager.clearCache()).called(1);
        expect(find.text('Cache cleared successfully'), findsOneWidget);
      });

      testWidgets('performance stats card clean expired button works', (tester) async {
        // Arrange
        when(mockApiManager.cleanExpiredCache()).thenAnswer((_) async {});

        // Act
        await tester.pumpWidget(createTestWidget(
          child: Scaffold(
            body: PerformanceStatsCard(apiManager: mockApiManager),
          ),
        ));
        await tester.pumpAndSettle();

        // Tap clean expired button
        await tester.tap(find.text('Clean Expired'));
        await tester.pumpAndSettle();

        // Assert
        verify(mockApiManager.cleanExpiredCache()).called(1);
        expect(find.text('Expired cache cleaned successfully'), findsOneWidget);
      });

      testWidgets('performance stats card handles loading state', (tester) async {
        // Arrange
        when(mockApiManager.getPerformanceStats())
            .thenAnswer((_) => Future.delayed(const Duration(seconds: 1), () => {}));

        // Act
        await tester.pumpWidget(createTestWidget(
          child: Scaffold(
            body: PerformanceStatsCard(apiManager: mockApiManager),
          ),
        ));
        
        // Check loading state
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
        
        await tester.pumpAndSettle();

        // Assert loading is gone
        expect(find.byType(CircularProgressIndicator), findsNothing);
      });

      testWidgets('performance stats card handles error state', (tester) async {
        // Arrange
        when(mockApiManager.getPerformanceStats())
            .thenThrow(Exception('Network error'));

        // Act
        await tester.pumpWidget(createTestWidget(
          child: Scaffold(
            body: PerformanceStatsCard(apiManager: mockApiManager),
          ),
        ));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Error loading stats'), findsOneWidget);
        expect(find.byIcon(Icons.error), findsOneWidget);
      });
    });

    group('Health Monitoring Card Tests', () {
      testWidgets('health monitoring card displays correctly', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget(
          child: Scaffold(
            body: HealthMonitoringCard(
              healthMonitor: mockHealthMonitor,
              errorTracker: mockErrorTracker,
            ),
          ),
        ));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('System Health Monitor'), findsOneWidget);
        expect(find.text('All systems operational'), findsOneWidget);
        expect(find.byIcon(Icons.check_circle), findsOneWidget);
      });

      testWidgets('health monitoring card displays healthy status', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget(
          child: Scaffold(
            body: HealthMonitoringCard(
              healthMonitor: mockHealthMonitor,
              errorTracker: mockErrorTracker,
            ),
          ),
        ));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Healthy'), findsOneWidget);
        expect(find.text('All systems operational'), findsOneWidget);
        expect(find.text('150ms'), findsOneWidget); // Response time
      });

      testWidgets('health monitoring card displays key metrics', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget(
          child: Scaffold(
            body: HealthMonitoringCard(
              healthMonitor: mockHealthMonitor,
              errorTracker: mockErrorTracker,
            ),
          ),
        ));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Key Metrics'), findsOneWidget);
        expect(find.text('Connection'), findsOneWidget);
        expect(find.text('Active'), findsOneWidget);
        expect(find.text('Models Available'), findsOneWidget);
        expect(find.text('3'), findsOneWidget);
        expect(find.text('Error Rate'), findsOneWidget);
        expect(find.text('0.5/hour'), findsOneWidget);
      });

      testWidgets('health monitoring card displays error summary', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget(
          child: Scaffold(
            body: HealthMonitoringCard(
              healthMonitor: mockHealthMonitor,
              errorTracker: mockErrorTracker,
            ),
          ),
        ));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Error Summary (Last Hour)'), findsOneWidget);
        expect(find.text('Total Errors'), findsOneWidget);
        expect(find.text('2'), findsOneWidget);
      });

      testWidgets('health monitoring card displays health trends', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget(
          child: Scaffold(
            body: HealthMonitoringCard(
              healthMonitor: mockHealthMonitor,
              errorTracker: mockErrorTracker,
            ),
          ),
        ));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Health Trends (6 Hours)'), findsOneWidget);
        expect(find.text('Trend'), findsOneWidget);
        expect(find.text('Stable'), findsOneWidget);
        expect(find.text('Uptime'), findsOneWidget);
        expect(find.text('99.5%'), findsOneWidget);
        expect(find.text('Stability'), findsOneWidget);
        expect(find.text('95.0%'), findsOneWidget);
      });

      testWidgets('health monitoring card refresh button works', (tester) async {
        // Arrange
        when(mockHealthMonitor.performHealthCheck()).thenAnswer((_) async =>
          HealthCheckResult(
            status: HealthStatus.healthy,
            timestamp: DateTime.now(),
            responseTime: const Duration(milliseconds: 200),
            metrics: {'connected': true},
            issues: [],
            statusMessage: 'Refreshed status',
          ),
        );

        // Act
        await tester.pumpWidget(createTestWidget(
          child: Scaffold(
            body: HealthMonitoringCard(
              healthMonitor: mockHealthMonitor,
              errorTracker: mockErrorTracker,
            ),
          ),
        ));
        await tester.pumpAndSettle();

        // Tap refresh button
        await tester.tap(find.byIcon(Icons.refresh));
        await tester.pumpAndSettle();

        // Assert
        verify(mockHealthMonitor.performHealthCheck()).called(1);
      });

      testWidgets('health monitoring card shows critical status correctly', (tester) async {
        // Arrange
        when(mockHealthMonitor.currentHealth).thenReturn(
          HealthCheckResult(
            status: HealthStatus.critical,
            timestamp: DateTime.now(),
            responseTime: const Duration(milliseconds: 5000),
            metrics: {'connected': false, 'available_models': 0},
            issues: ['Ollama server is not accessible'],
            statusMessage: 'System failure: Server unavailable',
          ),
        );

        // Act
        await tester.pumpWidget(createTestWidget(
          child: Scaffold(
            body: HealthMonitoringCard(
              healthMonitor: mockHealthMonitor,
              errorTracker: mockErrorTracker,
            ),
          ),
        ));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Critical'), findsOneWidget);
        expect(find.text('System failure: Server unavailable'), findsOneWidget);
        expect(find.byIcon(Icons.warning), findsOneWidget);
        expect(find.text('Current Issues'), findsOneWidget);
        expect(find.text('Ollama server is not accessible'), findsOneWidget);
      });

      testWidgets('health monitoring card handles loading state', (tester) async {
        // Arrange
        when(mockHealthMonitor.currentHealth).thenReturn(null);
        when(mockHealthMonitor.performHealthCheck()).thenAnswer((_) => 
          Future.delayed(const Duration(seconds: 1), () => 
            HealthCheckResult(
              status: HealthStatus.healthy,
              timestamp: DateTime.now(),
              responseTime: Duration.zero,
              metrics: {},
              issues: [],
              statusMessage: 'Loaded',
            ),
          ),
        );

        // Act
        await tester.pumpWidget(createTestWidget(
          child: Scaffold(
            body: HealthMonitoringCard(
              healthMonitor: mockHealthMonitor,
              errorTracker: mockErrorTracker,
            ),
          ),
        ));

        // Check loading state
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
        
        await tester.pumpAndSettle();

        // Assert loading is gone
        expect(find.byType(CircularProgressIndicator), findsNothing);
      });
    });

    group('Integration Tests', () {
      testWidgets('both cards work together in settings page', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert both cards are present and functional
        expect(find.byType(PerformanceStatsCard), findsOneWidget);
        expect(find.byType(HealthMonitoringCard), findsOneWidget);
        
        // Test interaction with both cards
        expect(find.text('Performance Statistics'), findsOneWidget);
        expect(find.text('System Health Monitor'), findsOneWidget);
      });

      testWidgets('cards update independently', (tester) async {
        // This would test that the cards can update their data independently
        // without affecting each other
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Verify initial state
        expect(find.text('All systems operational'), findsOneWidget);
        
        // In a real test, we'd simulate data changes and verify updates
        expect(true, isTrue); // Placeholder
      });

      testWidgets('error states are handled gracefully across cards', (tester) async {
        // Arrange - simulate errors in both cards
        when(mockApiManager.getPerformanceStats())
            .thenThrow(Exception('Performance error'));
        when(mockHealthMonitor.performHealthCheck())
            .thenThrow(Exception('Health check error'));

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert both cards handle errors
        expect(find.text('Error loading stats'), findsOneWidget);
        // Health card would show its error state too
      });
    });

    group('Responsive Design Tests', () {
      testWidgets('cards adapt to different screen sizes', (tester) async {
        // Test with different screen sizes
        await tester.binding.setSurfaceSize(const Size(320, 568)); // Small screen
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Cards should still be visible and usable
        expect(find.byType(PerformanceStatsCard), findsOneWidget);
        expect(find.byType(HealthMonitoringCard), findsOneWidget);

        // Test with larger screen
        await tester.binding.setSurfaceSize(const Size(768, 1024)); // Tablet size
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Cards should adapt to larger screen
        expect(find.byType(PerformanceStatsCard), findsOneWidget);
        expect(find.byType(HealthMonitoringCard), findsOneWidget);
      });

      testWidgets('text scales appropriately', (tester) async {
        // Test with different text scale factors
        await tester.pumpWidget(
          MediaQuery(
            data: const MediaQueryData(textScaler: TextScaler.linear(1.5)),
            child: createTestWidget(),
          ),
        );
        await tester.pumpAndSettle();

        // Text should still be readable and not overflow
        expect(find.text('Performance Statistics'), findsOneWidget);
        expect(find.text('System Health Monitor'), findsOneWidget);
      });
    });

    group('Accessibility Tests', () {
      testWidgets('cards have proper semantic labels', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert semantic information is available
        expect(find.byType(Card), findsNWidgets(2));
        
        // Check for accessibility features
        expect(find.byType(IconButton), findsNWidgets(3)); // Refresh buttons
        
        // Verify tooltips are present
        expect(find.byTooltip('Refresh statistics'), findsOneWidget);
        expect(find.byTooltip('Refresh health data'), findsOneWidget);
      });

      testWidgets('buttons are accessible with screen readers', (tester) async {
        // This would test screen reader compatibility
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Find accessible buttons
        final refreshButtons = find.byType(IconButton);
        expect(refreshButtons, findsNWidgets(3));

        // Verify buttons can be activated
        for (final button in refreshButtons.evaluate()) {
          final widget = button.widget as IconButton;
          expect(widget.onPressed, isNotNull);
        }
      });
    });
  });
}