# Ollama Integration Test Suite

This directory contains comprehensive tests for the Ollama integration that replaces Claude CLI with local AI functionality.

## Test Structure

### Unit Tests (`test/unit/`)

**`ollama_api_manager_test.dart`**
- Tests core OllamaApiManager functionality
- Connection management and status tracking
- Model discovery and management
- Chat request handling and session management
- Performance optimization components (caching, connection pooling)
- Error handling and retry logic
- Backwards compatibility with Claude CLI interface

**`error_tracking_test.dart`**  
- Tests OllamaErrorTracker error monitoring system
- Error categorization and severity classification
- Error persistence and statistics calculation
- Health monitoring integration
- Memory management and cleanup

**`health_monitoring_test.dart`**
- Tests OllamaHealthMonitor system health tracking
- Health check execution and status determination
- Health history and trend analysis
- Performance metric integration
- Real-time monitoring streams

### Widget Tests (`test/widget/`)

**`settings_page_widget_test.dart`**
- Tests settings page UI components
- PerformanceStatsCard widget functionality
- HealthMonitoringCard widget functionality
- User interaction testing (refresh, clear cache)
- Error state handling in UI
- Responsive design and accessibility

### Integration Tests (`test/integration/`)

**`ollama_integration_test.dart`**
- End-to-end testing with real Ollama server
- Connection establishment and model detection
- Chat functionality and session management
- Performance benchmarking
- Real-world usage scenarios
- Error handling under various conditions

## Test Dependencies

Required test dependencies (already added to `pubspec.yaml`):

```yaml
dev_dependencies:
  mockito: ^5.4.4           # Mocking framework
  integration_test:         # Integration testing
    sdk: flutter
  provider: ^6.1.2          # State management for tests
```

## Running Tests

### Unit Tests
```bash
# Run all unit tests
flutter test test/unit/

# Run specific test file
flutter test test/unit/ollama_api_manager_test.dart
flutter test test/unit/error_tracking_test.dart
flutter test test/unit/health_monitoring_test.dart
```

### Widget Tests
```bash
# Run all widget tests
flutter test test/widget/

# Run specific widget test
flutter test test/widget/settings_page_widget_test.dart
```

### Integration Tests
```bash
# Run integration tests (requires running Ollama server)
flutter test integration_test/ollama_integration_test.dart

# Run with real device
flutter drive --driver=test_driver/integration_test.dart --target=integration_test/ollama_integration_test.dart
```

### All Tests
```bash
# Run all tests
flutter test

# Run with coverage
flutter test --coverage
```

## Test Setup Requirements

### For Unit Tests
- No external dependencies required
- Uses mocked components for isolated testing

### For Widget Tests  
- No external dependencies required
- Tests UI components in isolation using widget testing framework

### For Integration Tests
- **Requires running Ollama server on localhost:11434**
- Install Ollama from https://ollama.com
- Pull at least one model: `ollama pull llama3.2:3b`
- Start Ollama: `ollama serve`

## Mock Generation

Some tests use mocks generated by `mockito`. If you modify interfaces, regenerate mocks:

```bash
# Generate mocks (if test files are updated)
flutter packages pub run build_runner build
```

## Test Coverage Areas

### ✅ Implemented Test Coverage

1. **Connection Management**
   - Server availability checking
   - Connection status tracking
   - Retry logic with exponential backoff
   - Graceful error handling

2. **Model Management**
   - Model discovery and listing
   - Model recommendation system
   - Model availability checking
   - Model pulling functionality

3. **Chat Functionality**  
   - Basic chat requests
   - Session management
   - Conversation history
   - Context handling

4. **Performance Optimization**
   - Response caching
   - Connection pooling
   - Request debouncing/throttling
   - Performance statistics

5. **Error Monitoring**
   - Error categorization and tracking
   - Error statistics and analysis
   - System health monitoring
   - Health trend analysis

6. **UI Components**
   - Settings page functionality
   - Performance stats display
   - Health monitoring dashboard
   - User interactions

7. **Integration Scenarios**
   - End-to-end chat sessions
   - Model switching
   - Performance under load
   - Real-world usage patterns

### Test Scenarios Covered

**Connection Scenarios:**
- ✅ Server available and healthy
- ✅ Server unavailable/offline
- ✅ Server running but no models
- ✅ Network timeouts and retries
- ✅ Connection status transitions

**Chat Scenarios:**
- ✅ Successful chat requests
- ✅ Error handling (invalid model, server errors)
- ✅ Session-based conversations
- ✅ Context file handling
- ✅ Response caching

**Performance Scenarios:**
- ✅ Cache hit/miss behavior
- ✅ Connection pool usage
- ✅ Request throttling
- ✅ Memory management
- ✅ Performance metrics collection

**Error Scenarios:**
- ✅ Network failures
- ✅ Server errors
- ✅ Invalid requests
- ✅ Timeout handling
- ✅ Error categorization and tracking

**UI Scenarios:**
- ✅ Component rendering
- ✅ Data loading states
- ✅ Error state display
- ✅ User interactions
- ✅ Responsive design

## Expected Test Results

When running tests with a properly configured Ollama server:
- **Unit Tests**: Should pass completely (use mocks)
- **Widget Tests**: Should pass completely (isolated UI testing)
- **Integration Tests**: Should pass if Ollama server is running with models

When running tests without Ollama server:
- **Unit Tests**: Should pass completely (use mocks)
- **Widget Tests**: Should pass completely (isolated UI testing)  
- **Integration Tests**: Should gracefully handle server unavailable scenarios

## Troubleshooting

### Common Issues

**Test dependency errors:**
```bash
flutter clean
flutter pub get
```

**Mock generation errors:**
```bash
flutter packages pub run build_runner clean
flutter packages pub run build_runner build
```

**Integration test failures:**
1. Ensure Ollama is installed and running
2. Verify models are available: `ollama list`
3. Check server accessibility: `curl http://localhost:11434/api/tags`

**Performance test variations:**
- Integration test performance may vary based on system resources
- Some timing-based tests may need adjustment for different hardware

## Test Quality Metrics

The test suite provides:
- **High code coverage** across core functionality
- **Multiple test types** (unit, widget, integration)
- **Error scenario coverage** for robust error handling
- **Performance validation** under various conditions  
- **UI/UX testing** for user-facing components
- **Real-world scenario testing** with actual Ollama integration

This comprehensive test suite ensures that the Ollama integration is reliable, performant, and provides a smooth user experience as a replacement for Claude CLI functionality.