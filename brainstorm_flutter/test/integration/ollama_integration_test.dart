import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import '../../lib/main.dart' as app;
import '../../lib/core/ollama_api/ollama_api_manager.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Ollama Integration Tests', () {
    late OllamaApiManager apiManager;

    setUpAll(() {
      apiManager = OllamaApiManager();
    });

    tearDownAll(() {
      apiManager.dispose();
    });

    group('Connection Tests', () {
      testWidgets('app launches successfully', (tester) async {
        // Launch the app
        app.main();
        await tester.pumpAndSettle();

        // Verify app launched
        expect(find.byType(MaterialApp), findsOneWidget);
      });

      testWidgets('ollama connection can be established', (tester) async {
        // This test requires a running Ollama server
        final isAvailable = await apiManager.isOllamaAvailable();
        
        if (isAvailable) {
          // If Ollama is available, test basic functionality
          expect(isAvailable, isTrue);
          
          final version = await apiManager.getOllamaVersion();
          expect(version, isNotNull);
          
          final models = await apiManager.getAvailableModels();
          expect(models, isA<List<OllamaModel>>());
        } else {
          // If Ollama is not available, verify graceful handling
          expect(isAvailable, isFalse);
          
          final status = await apiManager.getInstallationStatus();
          expect(status.isInstalled, isFalse);
          expect(status.recommendedAction, contains('Install'));
        }
      });
    });

    group('Model Management Tests', () {
      testWidgets('model detection works correctly', (tester) async {
        final isAvailable = await apiManager.isOllamaAvailable();
        
        if (isAvailable) {
          final models = await apiManager.getAvailableModels();
          
          if (models.isNotEmpty) {
            // Test model properties
            final firstModel = models.first;
            expect(firstModel.name, isNotEmpty);
            expect(firstModel.size, isNotEmpty);
            expect(firstModel.description, isNotEmpty);
            
            // Test model availability check
            final isModelAvailable = await apiManager.isModelAvailable(firstModel.name);
            expect(isModelAvailable, isTrue);
          }
          
          // Test recommended models
          final recommendedModels = await apiManager.getRecommendedModels();
          expect(recommendedModels, isA<List<OllamaModel>>());
        }
      });
    });

    group('Chat Functionality Tests', () {
      testWidgets('basic chat request works', (tester) async {
        final isAvailable = await apiManager.isOllamaAvailable();
        
        if (isAvailable) {
          final models = await apiManager.getAvailableModels();
          
          if (models.isNotEmpty) {
            final testModel = models.first.name;
            
            // Send a simple chat request
            final response = await apiManager.sendChatRequest(
              model: testModel,
              prompt: 'Hello! Please respond with a short greeting.',
            );
            
            expect(response.success, isTrue);
            expect(response.content, isNotNull);
            expect(response.content!.isNotEmpty, isTrue);
            expect(response.sessionId, isNotNull);
            expect(response.error, isNull);
            
            // Check metadata
            expect(response.metadata, isNotNull);
            expect(response.metadata!['model'], testModel);
            expect(response.metadata!['cached'], isFalse);
          }
        }
      });

      testWidgets('session management works correctly', (tester) async {
        final isAvailable = await apiManager.isOllamaAvailable();
        
        if (isAvailable) {
          final models = await apiManager.getAvailableModels();
          
          if (models.isNotEmpty) {
            // Start interactive session
            const sessionId = 'integration_test_session';
            final startedSessionId = await apiManager.startInteractiveSession(
              sessionId: sessionId,
            );
            
            expect(startedSessionId, sessionId);
            
            // Send multiple messages
            bool success1 = await apiManager.sendToInteractiveSession(
              sessionId,
              'My name is Integration Test.',
            );
            expect(success1, isTrue);
            
            bool success2 = await apiManager.sendToInteractiveSession(
              sessionId,
              'What is my name?',
            );
            expect(success2, isTrue);
            
            // Check session history
            final history = apiManager.getSessionHistory(sessionId);
            expect(history.length, greaterThanOrEqualTo(2)); // At least user messages
            
            // Close session
            await apiManager.closeSession(sessionId);
            final emptyHistory = apiManager.getSessionHistory(sessionId);
            expect(emptyHistory, isEmpty);
          }
        }
      });

      testWidgets('backwards compatibility with Claude API works', (tester) async {
        final isAvailable = await apiManager.isClaudeAvailable(); // Compatibility method
        
        if (isAvailable) {
          // Test compatibility methods
          final version = await apiManager.getClaudeVersion();
          expect(version, startsWith('ollama-'));
          
          final response = await apiManager.sendToClaudeCLI(
            prompt: 'Test compatibility',
          );
          
          expect(response, isA<OllamaResponse>());
          expect(response.sessionId, isNotNull);
        }
      });
    });

    group('Performance Tests', () {
      testWidgets('caching improves performance', (tester) async {
        final isAvailable = await apiManager.isOllamaAvailable();
        
        if (isAvailable) {
          final models = await apiManager.getAvailableModels();
          
          if (models.isNotEmpty) {
            const testPrompt = 'What is 2+2?';
            final testModel = models.first.name;
            
            // First request (uncached)
            final stopwatch1 = Stopwatch()..start();
            final response1 = await apiManager.sendChatRequest(
              model: testModel,
              prompt: testPrompt,
              enableCaching: true,
            );
            stopwatch1.stop();
            
            expect(response1.success, isTrue);
            expect(response1.metadata!['cached'], isFalse);
            
            // Second identical request (should be cached)
            final stopwatch2 = Stopwatch()..start();
            final response2 = await apiManager.sendChatRequest(
              model: testModel,
              prompt: testPrompt,
              enableCaching: true,
            );
            stopwatch2.stop();
            
            expect(response2.success, isTrue);
            
            // Cached response should be faster (though this is not guaranteed)
            // We mainly check that caching doesn't break functionality
            expect(response2.content, isNotNull);
          }
        }
      });

      testWidgets('performance stats are collected', (tester) async {
        final stats = await apiManager.getPerformanceStats();
        
        expect(stats, isA<Map<String, dynamic>>());
        expect(stats.containsKey('cache'), isTrue);
        expect(stats.containsKey('connectionPool'), isTrue);
        expect(stats.containsKey('sessionHistory'), isTrue);
        expect(stats.containsKey('throttler'), isTrue);
        
        // Test cache operations
        await apiManager.clearCache();
        await apiManager.cleanExpiredCache();
        
        // Get updated stats
        final newStats = await apiManager.getPerformanceStats();
        expect(newStats, isA<Map<String, dynamic>>());
      });
    });

    group('Error Handling Tests', () {
      testWidgets('handles server unavailable gracefully', (tester) async {
        // Create manager with invalid URL
        final invalidApiManager = OllamaApiManager(baseUrl: 'http://localhost:99999');
        
        final isAvailable = await invalidApiManager.isOllamaAvailable();
        expect(isAvailable, isFalse);
        
        final models = await invalidApiManager.getAvailableModels();
        expect(models, isEmpty);
        
        final response = await invalidApiManager.sendChatRequest(
          model: 'any-model',
          prompt: 'test',
        );
        
        expect(response.success, isFalse);
        expect(response.error, isNotNull);
        expect(response.error, contains('Connection error'));
        
        invalidApiManager.dispose();
      });

      testWidgets('handles invalid model gracefully', (tester) async {
        final isAvailable = await apiManager.isOllamaAvailable();
        
        if (isAvailable) {
          final response = await apiManager.sendChatRequest(
            model: 'non-existent-model',
            prompt: 'test',
          );
          
          // Should handle invalid model gracefully
          expect(response, isA<OllamaResponse>());
          // Response might succeed (Ollama may auto-pull) or fail gracefully
        }
      });

      testWidgets('connection status is tracked correctly', (tester) async {
        expect(apiManager.connectionStatus, isA<OllamaConnectionStatus>());
        
        // After checking availability, status should be updated
        await apiManager.isOllamaAvailable();
        
        final status = apiManager.connectionStatus;
        expect(status, isIn([
          OllamaConnectionStatus.connected,
          OllamaConnectionStatus.failed,
        ]));
        
        if (status == OllamaConnectionStatus.failed) {
          expect(apiManager.lastError, isNotNull);
        }
      });
    });

    group('Installation Status Tests', () {
      testWidgets('installation status provides comprehensive info', (tester) async {
        final status = await apiManager.getInstallationStatus();
        
        expect(status, isA<OllamaInstallationStatus>());
        expect(status.statusMessage, isNotEmpty);
        expect(status.recommendedAction, isNotEmpty);
        
        if (status.isInstalled && status.isRunning) {
          expect(status.version, isNotNull);
          expect(status.modelCount, isNotNull);
          expect(status.availableModels, isNotNull);
          
          if (status.hasModels) {
            expect(status.modelCount!, greaterThan(0));
            expect(status.availableModels!.isNotEmpty, isTrue);
            expect(status.isFullyConfigured, isTrue);
          }
        } else {
          expect(status.isFullyConfigured, isFalse);
          expect(status.recommendedAction, contains('Install'));
        }
      });
    });

    group('App Integration Tests', () {
      testWidgets('app works with ollama integration', (tester) async {
        // Launch the app
        app.main();
        await tester.pumpAndSettle();

        // Navigate to settings page (if accessible)
        // This would depend on the app's navigation structure
        
        // Try to find settings-related elements
        final settingsFinder = find.text('Settings');
        if (settingsFinder.evaluate().isNotEmpty) {
          await tester.tap(settingsFinder);
          await tester.pumpAndSettle();
          
          // Check if performance stats are shown
          expect(find.text('Performance Statistics'), findsOneWidget);
          
          // Check if health monitoring is shown
          expect(find.text('System Health Monitor'), findsOneWidget);
        }
      });

      testWidgets('error monitoring works in real app', (tester) async {
        // This would test the error monitoring in the context of the full app
        app.main();
        await tester.pumpAndSettle();
        
        // The error monitoring system should be initialized and working
        // This is more of a smoke test to ensure no crashes occur
        expect(find.byType(MaterialApp), findsOneWidget);
      });
    });

    group('Performance Benchmarks', () {
      testWidgets('response times are reasonable', (tester) async {
        final isAvailable = await apiManager.isOllamaAvailable();
        
        if (isAvailable) {
          final models = await apiManager.getAvailableModels();
          
          if (models.isNotEmpty) {
            final testModel = models.first.name;
            
            // Test simple prompt response time
            final stopwatch = Stopwatch()..start();
            final response = await apiManager.sendChatRequest(
              model: testModel,
              prompt: 'Say hello',
            );
            stopwatch.stop();
            
            if (response.success) {
              // Response time should be reasonable (less than 30 seconds for simple prompt)
              expect(stopwatch.elapsed.inSeconds, lessThan(30));
              
              // Response should have timing metadata
              expect(response.metadata?['total_duration'], isNotNull);
              expect(response.metadata?['eval_count'], isNotNull);
            }
          }
        }
      });

      testWidgets('connection pooling works under load', (tester) async {
        final isAvailable = await apiManager.isOllamaAvailable();
        
        if (isAvailable) {
          final models = await apiManager.getAvailableModels();
          
          if (models.isNotEmpty) {
            final testModel = models.first.name;
            
            // Make multiple concurrent requests
            final futures = <Future<OllamaResponse>>[];
            for (int i = 0; i < 3; i++) {
              futures.add(apiManager.sendChatRequest(
                model: testModel,
                prompt: 'Test request $i',
              ));
            }
            
            final responses = await Future.wait(futures);
            
            // All requests should complete successfully
            for (final response in responses) {
              expect(response, isA<OllamaResponse>());
              expect(response.sessionId, isNotNull);
            }
            
            // Check connection pool stats
            final stats = await apiManager.getPerformanceStats();
            final poolStats = stats['connectionPool'] as Map<String, dynamic>?;
            if (poolStats != null) {
              expect(poolStats['active_connections'], isA<int>());
              expect(poolStats['idle_connections'], isA<int>());
            }
          }
        }
      });
    });

    group('Real-world Scenarios', () {
      testWidgets('brainstorming session simulation', (tester) async {
        final isAvailable = await apiManager.isOllamaAvailable();
        
        if (isAvailable) {
          final models = await apiManager.getAvailableModels();
          
          if (models.isNotEmpty) {
            final sessionId = 'brainstorm_${DateTime.now().millisecondsSinceEpoch}';
            await apiManager.startInteractiveSession(sessionId: sessionId);
            
            // Simulate a brainstorming conversation
            final prompts = [
              'I need ideas for a mobile app that helps people learn languages.',
              'What features would make this app unique?',
              'How could we use AI in this language learning app?',
            ];
            
            for (final prompt in prompts) {
              final success = await apiManager.sendToInteractiveSession(sessionId, prompt);
              expect(success, isTrue);
              
              // Small delay between messages
              await Future.delayed(const Duration(milliseconds: 100));
            }
            
            // Check conversation history
            final history = apiManager.getSessionHistory(sessionId);
            expect(history.length, greaterThanOrEqualTo(prompts.length));
            
            // Verify conversation flow
            for (int i = 0; i < prompts.length; i++) {
              final userMessageIndex = i * 2; // Assuming user/assistant alternating
              if (userMessageIndex < history.length) {
                expect(history[userMessageIndex].role, 'user');
                expect(history[userMessageIndex].content, prompts[i]);
              }
            }
            
            await apiManager.closeSession(sessionId);
          }
        }
      });

      testWidgets('model switching scenario', (tester) async {
        final isAvailable = await apiManager.isOllamaAvailable();
        
        if (isAvailable) {
          final models = await apiManager.getAvailableModels();
          
          if (models.length > 1) {
            const testPrompt = 'Write a haiku about technology.';
            
            // Test with different models
            for (final model in models.take(2)) {
              final response = await apiManager.sendChatRequest(
                model: model.name,
                prompt: testPrompt,
              );
              
              expect(response.success, isTrue);
              expect(response.metadata?['model'], model.name);
              
              // Each model should produce different responses
              expect(response.content, isNotNull);
              expect(response.content!.isNotEmpty, isTrue);
            }
          }
        }
      });
    });
  });
}