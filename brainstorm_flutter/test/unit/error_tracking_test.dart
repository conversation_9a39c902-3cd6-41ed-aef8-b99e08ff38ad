import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../lib/core/ollama_api/monitoring/error_tracker.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  group('OllamaErrorTracker Tests', () {
    late OllamaErrorTracker errorTracker;

    setUp(() async {
      // Mock SharedPreferences for testing
      SharedPreferences.setMockInitialValues({});
      errorTracker = OllamaErrorTracker();
      await errorTracker.initialize();
    });

    tearDown(() {
      errorTracker.dispose();
    });

    group('Error Event Tests', () {
      test('ErrorEvent.fromException creates event with auto-categorization', () {
        final exception = Exception('Connection refused');
        
        final event = ErrorEvent.fromException(
          exception,
          severity: ErrorSeverity.high,
          category: ErrorCategory.connection,
          operation: 'test_operation',
          context: {'url': 'localhost:11434'},
          duration: const Duration(milliseconds: 500),
        );

        expect(event.severity, ErrorSeverity.high);
        expect(event.category, ErrorCategory.connection);
        expect(event.message, contains('Connection refused'));
        expect(event.operation, 'test_operation');
        expect(event.context['url'], 'localhost:11434');
        expect(event.duration?.inMilliseconds, 500);
        expect(event.id, startsWith('err_'));
      });

      test('ErrorEvent toJson and fromJson work correctly', () {
        final originalEvent = ErrorEvent(
          id: 'test_error_1',
          timestamp: DateTime(2024, 1, 1, 12, 0, 0),
          severity: ErrorSeverity.medium,
          category: ErrorCategory.api,
          message: 'API request failed',
          stackTrace: 'Stack trace here',
          context: {'method': 'POST', 'endpoint': '/api/chat'},
          operation: 'chat_request',
          duration: const Duration(milliseconds: 1000),
        );

        final json = originalEvent.toJson();
        final recreatedEvent = ErrorEvent.fromJson(json);

        expect(recreatedEvent.id, originalEvent.id);
        expect(recreatedEvent.severity, originalEvent.severity);
        expect(recreatedEvent.category, originalEvent.category);
        expect(recreatedEvent.message, originalEvent.message);
        expect(recreatedEvent.stackTrace, originalEvent.stackTrace);
        expect(recreatedEvent.context, originalEvent.context);
        expect(recreatedEvent.operation, originalEvent.operation);
        expect(recreatedEvent.duration, originalEvent.duration);
      });
    });

    group('Error Tracking Tests', () {
      test('trackError stores and emits error events', () async {
        final errorEvent = ErrorEvent(
          id: 'test_1',
          timestamp: DateTime.now(),
          severity: ErrorSeverity.high,
          category: ErrorCategory.connection,
          message: 'Server unreachable',
        );

        // Listen to error stream
        final streamEvents = <ErrorEvent>[];
        final subscription = errorTracker.errorStream.listen((event) {
          streamEvents.add(event);
        });

        // Track error
        await errorTracker.trackError(errorEvent);

        // Verify error was emitted
        expect(streamEvents.length, 1);
        expect(streamEvents.first.id, 'test_1');
        expect(streamEvents.first.severity, ErrorSeverity.high);

        await subscription.cancel();
      });

      test('trackException auto-categorizes errors correctly', () async {
        final connectionError = Exception('connection refused');
        final modelError = Exception('model not found');
        final timeoutError = Exception('request timeout');
        final cacheError = Exception('cache error');

        await errorTracker.trackException(connectionError, operation: 'connect');
        await errorTracker.trackException(modelError, operation: 'load_model');
        await errorTracker.trackException(timeoutError, operation: 'api_call');
        await errorTracker.trackException(cacheError, operation: 'cache_read');

        final recentErrors = errorTracker.getRecentErrors(limit: 10);

        expect(recentErrors.length, 4);
        
        // Find errors by operation to test categorization
        final connectionErr = recentErrors.firstWhere((e) => e.operation == 'connect');
        final modelErr = recentErrors.firstWhere((e) => e.operation == 'load_model');
        final timeoutErr = recentErrors.firstWhere((e) => e.operation == 'api_call');
        final cacheErr = recentErrors.firstWhere((e) => e.operation == 'cache_read');

        expect(connectionErr.category, ErrorCategory.connection);
        expect(connectionErr.severity, ErrorSeverity.high);
        
        expect(modelErr.category, ErrorCategory.model);
        expect(modelErr.severity, ErrorSeverity.high);
        
        expect(timeoutErr.category, ErrorCategory.performance);
        expect(timeoutErr.severity, ErrorSeverity.medium);
        
        expect(cacheErr.category, ErrorCategory.cache);
        expect(cacheErr.severity, ErrorSeverity.low);
      });

      test('getErrorStats calculates statistics correctly', () async {
        // Track multiple errors with different severities and categories
        await errorTracker.trackException(
          Exception('critical error'),
          operation: 'critical_op',
        );
        
        await errorTracker.trackException(
          Exception('connection refused'),
          operation: 'connection_test',
        );
        
        await errorTracker.trackException(
          Exception('model timeout'),
          operation: 'model_load',
        );

        final stats = await errorTracker.getErrorStats(
          period: const Duration(hours: 1),
        );

        expect(stats.totalErrors, 3);
        expect(stats.errorRate, 3.0); // 3 errors in 1 hour
        expect(stats.errorsBySeverity.values.fold(0, (sum, count) => sum + count), 3);
        expect(stats.errorsByCategory.values.fold(0, (sum, count) => sum + count), 3);
        expect(stats.errorsByOperation.length, 3);
      });

      test('getRecentErrors filters correctly', () async {
        // Track errors with different severities
        await errorTracker.trackException(
          Exception('low severity error'),
          operation: 'low_op',
        );
        
        await errorTracker.trackException(
          Exception('connection refused'),
          operation: 'high_op',
        );

        // Test severity filtering
        final highSeverityErrors = errorTracker.getRecentErrors(
          minSeverity: ErrorSeverity.high,
        );
        
        expect(highSeverityErrors.length, 1);
        expect(highSeverityErrors.first.operation, 'high_op');

        // Test limit
        final limitedErrors = errorTracker.getRecentErrors(limit: 1);
        expect(limitedErrors.length, 1);

        // Test category filtering
        final connectionErrors = errorTracker.getRecentErrors(
          category: ErrorCategory.connection,
        );
        expect(connectionErrors.length, 1);
        expect(connectionErrors.first.category, ErrorCategory.connection);
      });
    });

    group('Health Monitoring Tests', () {
      test('isSystemHealthy identifies unhealthy conditions', () async {
        // Track many critical errors
        for (int i = 0; i < 6; i++) {
          await errorTracker.trackException(
            Exception('critical error $i'),
            operation: 'critical_test',
          );
        }

        final isHealthy = await errorTracker.isSystemHealthy();
        expect(isHealthy, isFalse);
      });

      test('isSystemHealthy returns true for healthy system', () async {
        // Track only a few low-severity errors
        await errorTracker.trackException(
          Exception('cache miss'),
          operation: 'cache_test',
        );

        final isHealthy = await errorTracker.isSystemHealthy();
        expect(isHealthy, isTrue);
      });

      test('getHealthStatus provides comprehensive health info', () async {
        await errorTracker.trackException(
          Exception('test error'),
          operation: 'health_test',
        );

        final healthStatus = await errorTracker.getHealthStatus();

        expect(healthStatus['healthy'], isA<bool>());
        expect(healthStatus['totalErrors'], isA<int>());
        expect(healthStatus['errorRate'], isA<double>());
        expect(healthStatus['criticalErrors'], isA<int>());
        expect(healthStatus['mainIssues'], isA<List>());
      });
    });

    group('Persistence Tests', () {
      test('errors persist across tracker restarts', () async {
        // Track an error
        await errorTracker.trackException(
          Exception('persistent error'),
          operation: 'persistence_test',
        );

        // Dispose and recreate tracker
        errorTracker.dispose();
        
        final newTracker = OllamaErrorTracker();
        await newTracker.initialize();

        // Check if error was persisted
        final persistedErrors = newTracker.getRecentErrors();
        expect(persistedErrors.length, 1);
        expect(persistedErrors.first.operation, 'persistence_test');

        newTracker.dispose();
      });

      test('clearErrors removes all stored errors', () async {
        // Track multiple errors
        await errorTracker.trackException(Exception('error 1'), operation: 'op1');
        await errorTracker.trackException(Exception('error 2'), operation: 'op2');

        expect(errorTracker.getRecentErrors().length, 2);

        // Clear errors
        await errorTracker.clearErrors();

        expect(errorTracker.getRecentErrors().length, 0);
      });
    });

    group('Error Statistics Tests', () {
      test('ErrorStats calculates metrics correctly', () {
        final stats = ErrorStats(
          totalErrors: 10,
          errorsBySeverity: {
            ErrorSeverity.critical: 2,
            ErrorSeverity.high: 3,
            ErrorSeverity.medium: 4,
            ErrorSeverity.low: 1,
          },
          errorsByCategory: {
            ErrorCategory.connection: 5,
            ErrorCategory.api: 3,
            ErrorCategory.model: 2,
          },
          errorsByOperation: {
            'operation1': 6,
            'operation2': 4,
          },
          errorRate: 5.0,
          avgResponseTime: const Duration(milliseconds: 500),
          lastError: DateTime.now(),
          topErrors: ['error1', 'error2', 'error3'],
        );

        expect(stats.totalErrors, 10);
        expect(stats.errorRate, 5.0);
        expect(stats.errorsBySeverity[ErrorSeverity.critical], 2);
        expect(stats.errorsByCategory[ErrorCategory.connection], 5);
        expect(stats.errorsByOperation['operation1'], 6);
        expect(stats.avgResponseTime.inMilliseconds, 500);
        expect(stats.topErrors.length, 3);
      });
    });

    group('Error Categorization Tests', () {
      test('categorizes connection errors correctly', () async {
        final errors = [
          Exception('connection refused'),
          Exception('network error'),
          Exception('server unavailable'),
        ];

        for (final error in errors) {
          await errorTracker.trackException(error);
        }

        final connectionErrors = errorTracker.getRecentErrors(
          category: ErrorCategory.connection,
        );

        expect(connectionErrors.length, 3);
        connectionErrors.forEach((error) {
          expect(error.category, ErrorCategory.connection);
          expect(error.severity, ErrorSeverity.high);
        });
      });

      test('categorizes performance errors correctly', () async {
        final errors = [
          Exception('timeout occurred'),
          Exception('slow response detected'),
          Exception('performance degradation'),
        ];

        for (final error in errors) {
          await errorTracker.trackException(error);
        }

        final performanceErrors = errorTracker.getRecentErrors(
          category: ErrorCategory.performance,
        );

        expect(performanceErrors.length, 3);
        performanceErrors.forEach((error) {
          expect(error.category, ErrorCategory.performance);
        });
      });

      test('categorizes model errors correctly', () async {
        final errors = [
          Exception('model not found'),
          Exception('invalid model specified'),
          Exception('ollama model error'),
        ];

        for (final error in errors) {
          await errorTracker.trackException(error);
        }

        final modelErrors = errorTracker.getRecentErrors(
          category: ErrorCategory.model,
        );

        expect(modelErrors.length, 3);
        modelErrors.forEach((error) {
          expect(error.category, ErrorCategory.model);
        });
      });
    });

    group('Memory Management Tests', () {
      test('limits stored errors to maximum count', () async {
        // Track more errors than the maximum
        for (int i = 0; i < 1100; i++) {
          await errorTracker.trackException(
            Exception('error $i'),
            operation: 'bulk_test',
          );
        }

        final allErrors = errorTracker.getRecentErrors();
        expect(allErrors.length, lessThanOrEqualTo(1000)); // maxStoredErrors
      });

      test('cleanup removes old errors', () async {
        // This test would need to manipulate time or wait for cleanup
        // In a real implementation, we'd mock the timer or provide a manual cleanup trigger
        expect(true, isTrue); // Placeholder for cleanup logic test
      });
    });
  });

  group('ErrorSeverity and ErrorCategory Tests', () {
    test('ErrorSeverity enum has correct values', () {
      expect(ErrorSeverity.values.length, 4);
      expect(ErrorSeverity.values, contains(ErrorSeverity.low));
      expect(ErrorSeverity.values, contains(ErrorSeverity.medium));
      expect(ErrorSeverity.values, contains(ErrorSeverity.high));
      expect(ErrorSeverity.values, contains(ErrorSeverity.critical));
    });

    test('ErrorCategory enum has correct values', () {
      expect(ErrorCategory.values.length, 7);
      expect(ErrorCategory.values, contains(ErrorCategory.connection));
      expect(ErrorCategory.values, contains(ErrorCategory.authentication));
      expect(ErrorCategory.values, contains(ErrorCategory.model));
      expect(ErrorCategory.values, contains(ErrorCategory.api));
      expect(ErrorCategory.values, contains(ErrorCategory.cache));
      expect(ErrorCategory.values, contains(ErrorCategory.performance));
      expect(ErrorCategory.values, contains(ErrorCategory.unknown));
    });

    test('severity comparison works correctly', () {
      expect(ErrorSeverity.critical.index > ErrorSeverity.high.index, isTrue);
      expect(ErrorSeverity.high.index > ErrorSeverity.medium.index, isTrue);
      expect(ErrorSeverity.medium.index > ErrorSeverity.low.index, isTrue);
    });
  });
}