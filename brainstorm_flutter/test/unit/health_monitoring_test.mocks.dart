// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in brainstorm_flutter/test/unit/health_monitoring_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:brainstorm_flutter/core/ollama_api/monitoring/error_tracker.dart'
    as _i3;
import 'package:brainstorm_flutter/core/ollama_api/ollama_api_manager.dart'
    as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i4;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeDuration_0 extends _i1.SmartFake implements Duration {
  _FakeDuration_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeOllamaResponse_1 extends _i1.SmartFake
    implements _i2.OllamaResponse {
  _FakeOllamaResponse_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeOllamaInstallationStatus_2 extends _i1.SmartFake
    implements _i2.OllamaInstallationStatus {
  _FakeOllamaInstallationStatus_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeErrorStats_3 extends _i1.SmartFake implements _i3.ErrorStats {
  _FakeErrorStats_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [OllamaApiManager].
///
/// See the documentation for Mockito's code generation for more information.
class MockOllamaApiManager extends _i1.Mock implements _i2.OllamaApiManager {
  MockOllamaApiManager() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get baseUrl =>
      (super.noSuchMethod(
            Invocation.getter(#baseUrl),
            returnValue: _i4.dummyValue<String>(
              this,
              Invocation.getter(#baseUrl),
            ),
          )
          as String);

  @override
  Duration get timeout =>
      (super.noSuchMethod(
            Invocation.getter(#timeout),
            returnValue: _FakeDuration_0(this, Invocation.getter(#timeout)),
          )
          as Duration);

  @override
  int get maxRetries =>
      (super.noSuchMethod(Invocation.getter(#maxRetries), returnValue: 0)
          as int);

  @override
  Duration get retryDelay =>
      (super.noSuchMethod(
            Invocation.getter(#retryDelay),
            returnValue: _FakeDuration_0(this, Invocation.getter(#retryDelay)),
          )
          as Duration);

  @override
  _i2.OllamaConnectionStatus get connectionStatus =>
      (super.noSuchMethod(
            Invocation.getter(#connectionStatus),
            returnValue: _i2.OllamaConnectionStatus.connected,
          )
          as _i2.OllamaConnectionStatus);

  @override
  bool get shouldRetryConnection =>
      (super.noSuchMethod(
            Invocation.getter(#shouldRetryConnection),
            returnValue: false,
          )
          as bool);

  @override
  _i5.Future<bool> isOllamaAvailable() =>
      (super.noSuchMethod(
            Invocation.method(#isOllamaAvailable, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<String?> getOllamaVersion() =>
      (super.noSuchMethod(
            Invocation.method(#getOllamaVersion, []),
            returnValue: _i5.Future<String?>.value(),
          )
          as _i5.Future<String?>);

  @override
  _i5.Future<List<_i2.OllamaModel>> getAvailableModels() =>
      (super.noSuchMethod(
            Invocation.method(#getAvailableModels, []),
            returnValue: _i5.Future<List<_i2.OllamaModel>>.value(
              <_i2.OllamaModel>[],
            ),
          )
          as _i5.Future<List<_i2.OllamaModel>>);

  @override
  _i5.Future<_i2.OllamaResponse> sendChatRequest({
    required String? model,
    required String? prompt,
    String? sessionId,
    List<String>? contextFiles,
    Map<String, String>? environment,
    bool? stream = false,
    bool? enableCaching = true,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendChatRequest, [], {
              #model: model,
              #prompt: prompt,
              #sessionId: sessionId,
              #contextFiles: contextFiles,
              #environment: environment,
              #stream: stream,
              #enableCaching: enableCaching,
            }),
            returnValue: _i5.Future<_i2.OllamaResponse>.value(
              _FakeOllamaResponse_1(
                this,
                Invocation.method(#sendChatRequest, [], {
                  #model: model,
                  #prompt: prompt,
                  #sessionId: sessionId,
                  #contextFiles: contextFiles,
                  #environment: environment,
                  #stream: stream,
                  #enableCaching: enableCaching,
                }),
              ),
            ),
          )
          as _i5.Future<_i2.OllamaResponse>);

  @override
  _i5.Future<_i2.OllamaResponse> sendToClaudeCLI({
    required String? prompt,
    String? sessionId,
    List<String>? contextFiles,
    Map<String, String>? environment,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendToClaudeCLI, [], {
              #prompt: prompt,
              #sessionId: sessionId,
              #contextFiles: contextFiles,
              #environment: environment,
            }),
            returnValue: _i5.Future<_i2.OllamaResponse>.value(
              _FakeOllamaResponse_1(
                this,
                Invocation.method(#sendToClaudeCLI, [], {
                  #prompt: prompt,
                  #sessionId: sessionId,
                  #contextFiles: contextFiles,
                  #environment: environment,
                }),
              ),
            ),
          )
          as _i5.Future<_i2.OllamaResponse>);

  @override
  _i5.Future<bool> isClaudeAvailable() =>
      (super.noSuchMethod(
            Invocation.method(#isClaudeAvailable, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<String?> getClaudeVersion() =>
      (super.noSuchMethod(
            Invocation.method(#getClaudeVersion, []),
            returnValue: _i5.Future<String?>.value(),
          )
          as _i5.Future<String?>);

  @override
  _i5.Future<String?> startInteractiveSession({
    required String? sessionId,
    List<String>? contextFiles,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#startInteractiveSession, [], {
              #sessionId: sessionId,
              #contextFiles: contextFiles,
            }),
            returnValue: _i5.Future<String?>.value(),
          )
          as _i5.Future<String?>);

  @override
  _i5.Future<bool> sendToInteractiveSession(
    String? sessionId,
    String? message,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#sendToInteractiveSession, [sessionId, message]),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<void> closeSession(String? sessionId) =>
      (super.noSuchMethod(
            Invocation.method(#closeSession, [sessionId]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> closeAllSessions() =>
      (super.noSuchMethod(
            Invocation.method(#closeAllSessions, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  List<_i2.OllamaMessage> getSessionHistory(String? sessionId) =>
      (super.noSuchMethod(
            Invocation.method(#getSessionHistory, [sessionId]),
            returnValue: <_i2.OllamaMessage>[],
          )
          as List<_i2.OllamaMessage>);

  @override
  _i5.Future<bool> isModelAvailable(String? modelName) =>
      (super.noSuchMethod(
            Invocation.method(#isModelAvailable, [modelName]),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<List<_i2.OllamaModel>> getRecommendedModels() =>
      (super.noSuchMethod(
            Invocation.method(#getRecommendedModels, []),
            returnValue: _i5.Future<List<_i2.OllamaModel>>.value(
              <_i2.OllamaModel>[],
            ),
          )
          as _i5.Future<List<_i2.OllamaModel>>);

  @override
  _i5.Future<bool> pullModel(String? modelName) =>
      (super.noSuchMethod(
            Invocation.method(#pullModel, [modelName]),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<Map<String, dynamic>?> getServerInfo() =>
      (super.noSuchMethod(
            Invocation.method(#getServerInfo, []),
            returnValue: _i5.Future<Map<String, dynamic>?>.value(),
          )
          as _i5.Future<Map<String, dynamic>?>);

  @override
  _i5.Future<_i2.OllamaInstallationStatus> getInstallationStatus() =>
      (super.noSuchMethod(
            Invocation.method(#getInstallationStatus, []),
            returnValue: _i5.Future<_i2.OllamaInstallationStatus>.value(
              _FakeOllamaInstallationStatus_2(
                this,
                Invocation.method(#getInstallationStatus, []),
              ),
            ),
          )
          as _i5.Future<_i2.OllamaInstallationStatus>);

  @override
  _i5.Future<Map<String, dynamic>> getPerformanceStats() =>
      (super.noSuchMethod(
            Invocation.method(#getPerformanceStats, []),
            returnValue: _i5.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<void> clearCache() =>
      (super.noSuchMethod(
            Invocation.method(#clearCache, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> cleanExpiredCache() =>
      (super.noSuchMethod(
            Invocation.method(#cleanExpiredCache, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [OllamaErrorTracker].
///
/// See the documentation for Mockito's code generation for more information.
class MockOllamaErrorTracker extends _i1.Mock
    implements _i3.OllamaErrorTracker {
  MockOllamaErrorTracker() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Stream<_i3.ErrorEvent> get errorStream =>
      (super.noSuchMethod(
            Invocation.getter(#errorStream),
            returnValue: _i5.Stream<_i3.ErrorEvent>.empty(),
          )
          as _i5.Stream<_i3.ErrorEvent>);

  @override
  _i5.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> trackError(_i3.ErrorEvent? error) =>
      (super.noSuchMethod(
            Invocation.method(#trackError, [error]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> trackException(
    Exception? exception, {
    String? operation,
    Map<String, dynamic>? context = const {},
    Duration? duration,
    StackTrace? stackTrace,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #trackException,
              [exception],
              {
                #operation: operation,
                #context: context,
                #duration: duration,
                #stackTrace: stackTrace,
              },
            ),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<_i3.ErrorStats> getErrorStats({Duration? period}) =>
      (super.noSuchMethod(
            Invocation.method(#getErrorStats, [], {#period: period}),
            returnValue: _i5.Future<_i3.ErrorStats>.value(
              _FakeErrorStats_3(
                this,
                Invocation.method(#getErrorStats, [], {#period: period}),
              ),
            ),
          )
          as _i5.Future<_i3.ErrorStats>);

  @override
  List<_i3.ErrorEvent> getRecentErrors({
    int? limit,
    _i3.ErrorSeverity? minSeverity,
    _i3.ErrorCategory? category,
    Duration? since,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getRecentErrors, [], {
              #limit: limit,
              #minSeverity: minSeverity,
              #category: category,
              #since: since,
            }),
            returnValue: <_i3.ErrorEvent>[],
          )
          as List<_i3.ErrorEvent>);

  @override
  _i5.Future<void> clearErrors() =>
      (super.noSuchMethod(
            Invocation.method(#clearErrors, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<bool> isSystemHealthy() =>
      (super.noSuchMethod(
            Invocation.method(#isSystemHealthy, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<Map<String, dynamic>> getHealthStatus() =>
      (super.noSuchMethod(
            Invocation.method(#getHealthStatus, []),
            returnValue: _i5.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i5.Future<Map<String, dynamic>>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}
