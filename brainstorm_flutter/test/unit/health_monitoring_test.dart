import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import '../../lib/core/ollama_api/ollama_api_manager.dart';
import '../../lib/core/ollama_api/monitoring/error_tracker.dart';
import '../../lib/core/ollama_api/monitoring/health_monitor.dart';

// Generate mocks with: flutter packages pub run build_runner build
@GenerateMocks([OllamaApiManager, OllamaErrorTracker])
import 'health_monitoring_test.mocks.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  group('OllamaHealthMonitor Tests', () {
    late OllamaHealthMonitor healthMonitor;
    late MockOllamaApiManager mockApiManager;
    late MockOllamaErrorTracker mockErrorTracker;

    setUp(() {
      mockApiManager = MockOllamaApiManager();
      mockErrorTracker = MockOllamaErrorTracker();
      healthMonitor = OllamaHealthMonitor(mockApiManager, mockErrorTracker);
    });

    tearDown(() {
      healthMonitor.dispose();
    });

    group('Health Check Tests', () {
      test('performHealthCheck returns healthy status when all systems operational', () async {
        // Arrange
        when(mockApiManager.isOllamaAvailable()).thenAnswer((_) async => true);
        when(mockApiManager.getAvailableModels()).thenAnswer((_) async => [
          OllamaModel(
            name: 'gemma3:4b',
            size: '2GB',
            digest: 'abc123',
            modifiedAt: DateTime.now(),
          )
        ]);
        when(mockErrorTracker.getErrorStats(period: anyNamed('period')))
            .thenAnswer((_) async => ErrorStats(
              totalErrors: 0,
              errorsBySeverity: {},
              errorsByCategory: {},
              errorsByOperation: {},
              errorRate: 0.0,
              avgResponseTime: Duration.zero,
              topErrors: [],
            ));
        when(mockApiManager.getPerformanceStats()).thenAnswer((_) async => {
          'cache': {'memoryCacheSize': 5, 'persistentCacheSize': 10},
          'connectionPool': {'active_connections': 1, 'idle_connections': 2, 'max_connections': 5},
        });

        // Act
        final result = await healthMonitor.performHealthCheck();

        // Assert
        expect(result.status, HealthStatus.healthy);
        expect(result.isHealthy, isTrue);
        expect(result.issues, isEmpty);
        expect(result.statusMessage, 'All systems operational');
        expect(result.metrics['connected'], isTrue);
        expect(result.metrics['available_models'], 1);
        expect(result.metrics['error_rate'], 0.0);
      });

      test('performHealthCheck returns critical status when server unavailable', () async {
        // Arrange
        when(mockApiManager.isOllamaAvailable()).thenAnswer((_) async => false);
        when(mockErrorTracker.getErrorStats(period: anyNamed('period')))
            .thenAnswer((_) async => ErrorStats(
              totalErrors: 0,
              errorsBySeverity: {},
              errorsByCategory: {},
              errorsByOperation: {},
              errorRate: 0.0,
              avgResponseTime: Duration.zero,
              topErrors: [],
            ));

        // Act
        final result = await healthMonitor.performHealthCheck();

        // Assert
        expect(result.status, HealthStatus.critical);
        expect(result.isCritical, isTrue);
        expect(result.issues, contains('Ollama server is not accessible'));
        expect(result.statusMessage, contains('System failure'));
        expect(result.metrics['connected'], isFalse);
        expect(result.metrics['available_models'], 0);
      });

      test('performHealthCheck returns degraded status with minor issues', () async {
        // Arrange
        when(mockApiManager.isOllamaAvailable()).thenAnswer((_) async => true);
        when(mockApiManager.getAvailableModels()).thenAnswer((_) async => [
          OllamaModel(
            name: 'gemma3:4b',
            size: '2GB',
            digest: 'abc123',
            modifiedAt: DateTime.now(),
          )
        ]);
        when(mockErrorTracker.getErrorStats(period: anyNamed('period')))
            .thenAnswer((_) async => ErrorStats(
              totalErrors: 5,
              errorsBySeverity: {ErrorSeverity.medium: 5},
              errorsByCategory: {ErrorCategory.performance: 5},
              errorsByOperation: {'test_op': 5},
              errorRate: 6.0, // Slightly above threshold
              avgResponseTime: const Duration(milliseconds: 100),
              topErrors: ['performance issue'],
            ));
        when(mockApiManager.getPerformanceStats()).thenAnswer((_) async => {
          'cache': {'memoryCacheSize': 5},
          'connectionPool': {'active_connections': 1, 'max_connections': 5},
        });

        // Act
        final result = await healthMonitor.performHealthCheck();

        // Assert
        expect(result.status, HealthStatus.degraded);
        expect(result.isDegraded, isTrue);
        expect(result.statusMessage, contains('operational with minor issues'));
        expect(result.metrics['error_rate'], 6.0);
      });

      test('performHealthCheck returns unhealthy status with high error rate', () async {
        // Arrange
        when(mockApiManager.isOllamaAvailable()).thenAnswer((_) async => true);
        when(mockApiManager.getAvailableModels()).thenAnswer((_) async => []);
        when(mockErrorTracker.getErrorStats(period: anyNamed('period')))
            .thenAnswer((_) async => ErrorStats(
              totalErrors: 25,
              errorsBySeverity: {
                ErrorSeverity.critical: 3,
                ErrorSeverity.high: 10,
                ErrorSeverity.medium: 12,
              },
              errorsByCategory: {ErrorCategory.connection: 25},
              errorsByOperation: {'connection_test': 25},
              errorRate: 25.0, // High error rate
              avgResponseTime: const Duration(milliseconds: 200),
              topErrors: ['connection issues'],
            ));
        when(mockApiManager.getPerformanceStats()).thenAnswer((_) async => {});

        // Act
        final result = await healthMonitor.performHealthCheck();

        // Assert
        expect(result.status, HealthStatus.unhealthy);
        expect(result.isUnhealthy, isTrue);
        expect(result.issues, contains('No AI models are available'));
        expect(result.statusMessage, contains('experiencing problems'));
      });

      test('performHealthCheck handles exceptions gracefully', () async {
        // Arrange
        when(mockApiManager.isOllamaAvailable()).thenThrow(Exception('Network error'));
        when(mockErrorTracker.trackException(
          any,
          operation: anyNamed('operation'),
          context: anyNamed('context'),
        )).thenAnswer((_) async {});

        // Act
        final result = await healthMonitor.performHealthCheck();

        // Assert
        expect(result.status, HealthStatus.critical);
        expect(result.issues, contains('Health check failed'));
        expect(result.statusMessage, 'System health check failed');
        
        // Verify error was tracked
        verify(mockErrorTracker.trackException(
          any,
          operation: 'health_check',
          context: anyNamed('context'),
        )).called(1);
      });
    });

    group('Health Monitoring Tests', () {
      test('startMonitoring begins periodic health checks', () async {
        // Arrange
        when(mockApiManager.isOllamaAvailable()).thenAnswer((_) async => true);
        when(mockApiManager.getAvailableModels()).thenAnswer((_) async => []);
        when(mockErrorTracker.getErrorStats(period: anyNamed('period')))
            .thenAnswer((_) async => ErrorStats(
              totalErrors: 0,
              errorsBySeverity: {},
              errorsByCategory: {},
              errorsByOperation: {},
              errorRate: 0.0,
              avgResponseTime: Duration.zero,
              topErrors: [],
            ));
        when(mockApiManager.getPerformanceStats()).thenAnswer((_) async => {});

        // Act
        healthMonitor.startMonitoring();

        // Wait a bit for initial check
        await Future.delayed(const Duration(milliseconds: 100));

        // Assert
        expect(healthMonitor.currentHealth, isNotNull);
        expect(healthMonitor.healthHistory, isNotEmpty);
      });

      test('stopMonitoring stops periodic checks', () {
        // Act
        healthMonitor.startMonitoring();
        healthMonitor.stopMonitoring();

        // Assert - monitoring should be stopped (hard to test timers directly)
        expect(true, isTrue); // Placeholder
      });

      test('health stream emits health check results', () async {
        // Arrange
        when(mockApiManager.isOllamaAvailable()).thenAnswer((_) async => true);
        when(mockApiManager.getAvailableModels()).thenAnswer((_) async => []);
        when(mockErrorTracker.getErrorStats(period: anyNamed('period')))
            .thenAnswer((_) async => ErrorStats(
              totalErrors: 0,
              errorsBySeverity: {},
              errorsByCategory: {},
              errorsByOperation: {},
              errorRate: 0.0,
              avgResponseTime: Duration.zero,
              topErrors: [],
            ));
        when(mockApiManager.getPerformanceStats()).thenAnswer((_) async => {});

        final healthResults = <HealthCheckResult>[];
        final subscription = healthMonitor.healthStream.listen((result) {
          healthResults.add(result);
        });

        // Act
        await healthMonitor.performHealthCheck();

        // Assert
        expect(healthResults.length, 1);
        expect(healthResults.first, isA<HealthCheckResult>());

        await subscription.cancel();
      });
    });

    group('Health History and Trends Tests', () {
      test('getUptimePercentage calculates correctly', () async {
        // Arrange - create some health history
        when(mockApiManager.isOllamaAvailable()).thenAnswer((_) async => true);
        when(mockApiManager.getAvailableModels()).thenAnswer((_) async => []);
        when(mockErrorTracker.getErrorStats(period: anyNamed('period')))
            .thenAnswer((_) async => ErrorStats(
              totalErrors: 0,
              errorsBySeverity: {},
              errorsByCategory: {},
              errorsByOperation: {},
              errorRate: 0.0,
              avgResponseTime: Duration.zero,
              topErrors: [],
            ));
        when(mockApiManager.getPerformanceStats()).thenAnswer((_) async => {});

        // Perform multiple health checks
        await healthMonitor.performHealthCheck();
        await healthMonitor.performHealthCheck();
        await healthMonitor.performHealthCheck();

        // Act
        final uptime = healthMonitor.getUptimePercentage(
          period: const Duration(hours: 1),
        );

        // Assert
        expect(uptime, isA<double>());
        expect(uptime, greaterThanOrEqualTo(0.0));
        expect(uptime, lessThanOrEqualTo(100.0));
      });

      test('getAverageResponseTime calculates correctly', () async {
        // Arrange
        when(mockApiManager.isOllamaAvailable()).thenAnswer((_) async => true);
        when(mockApiManager.getAvailableModels()).thenAnswer((_) async => []);
        when(mockErrorTracker.getErrorStats(period: anyNamed('period')))
            .thenAnswer((_) async => ErrorStats(
              totalErrors: 0,
              errorsBySeverity: {},
              errorsByCategory: {},
              errorsByOperation: {},
              errorRate: 0.0,
              avgResponseTime: Duration.zero,
              topErrors: [],
            ));
        when(mockApiManager.getPerformanceStats()).thenAnswer((_) async => {});

        // Perform multiple health checks to build history
        await healthMonitor.performHealthCheck();
        await healthMonitor.performHealthCheck();

        // Act
        final avgResponseTime = healthMonitor.getAverageResponseTime(
          period: const Duration(hours: 1),
        );

        // Assert
        expect(avgResponseTime, isA<Duration>());
        expect(avgResponseTime.inMilliseconds, greaterThanOrEqualTo(0));
      });

      test('getHealthTrends analyzes health patterns', () async {
        // Arrange
        when(mockApiManager.isOllamaAvailable()).thenAnswer((_) async => true);
        when(mockApiManager.getAvailableModels()).thenAnswer((_) async => []);
        when(mockErrorTracker.getErrorStats(period: anyNamed('period')))
            .thenAnswer((_) async => ErrorStats(
              totalErrors: 0,
              errorsBySeverity: {},
              errorsByCategory: {},
              errorsByOperation: {},
              errorRate: 0.0,
              avgResponseTime: Duration.zero,
              topErrors: [],
            ));
        when(mockApiManager.getPerformanceStats()).thenAnswer((_) async => {});

        // Build some health history
        for (int i = 0; i < 5; i++) {
          await healthMonitor.performHealthCheck();
          await Future.delayed(const Duration(milliseconds: 10));
        }

        // Act
        final trends = healthMonitor.getHealthTrends(
          period: const Duration(hours: 6),
        );

        // Assert
        expect(trends, isA<Map<String, dynamic>>());
        expect(trends.containsKey('trend'), isTrue);
        expect(trends.containsKey('stability'), isTrue);
        expect(trends.containsKey('uptime'), isTrue);
        expect(trends.containsKey('avg_response_time'), isTrue);
        expect(trends.containsKey('total_checks'), isTrue);
        expect(trends.containsKey('status_distribution'), isTrue);

        expect(trends['trend'], isIn(['improving', 'declining', 'stable']));
        expect(trends['stability'], isA<double>());
        expect(trends['uptime'], isA<double>());
      });

      test('health history is limited to maximum entries', () async {
        // This would test the maxHealthHistory limit
        // In practice, would need to perform many health checks
        expect(healthMonitor.healthHistory.length, lessThanOrEqualTo(100));
      });
    });

    group('Health Status Classification Tests', () {
      test('determines critical status for server unavailable', () async {
        // Arrange
        when(mockApiManager.isOllamaAvailable()).thenAnswer((_) async => false);
        when(mockErrorTracker.getErrorStats(period: anyNamed('period')))
            .thenAnswer((_) async => ErrorStats(
              totalErrors: 0,
              errorsBySeverity: {},
              errorsByCategory: {},
              errorsByOperation: {},
              errorRate: 0.0,
              avgResponseTime: Duration.zero,
              topErrors: [],
            ));

        // Act
        final result = await healthMonitor.performHealthCheck();

        // Assert
        expect(result.status, HealthStatus.critical);
      });

      test('determines unhealthy status for high error rates', () async {
        // Arrange
        when(mockApiManager.isOllamaAvailable()).thenAnswer((_) async => true);
        when(mockApiManager.getAvailableModels()).thenAnswer((_) async => []);
        when(mockErrorTracker.getErrorStats(period: anyNamed('period')))
            .thenAnswer((_) async => ErrorStats(
              totalErrors: 50,
              errorsBySeverity: {ErrorSeverity.critical: 5},
              errorsByCategory: {},
              errorsByOperation: {},
              errorRate: 25.0, // High rate
              avgResponseTime: Duration.zero,
              topErrors: [],
            ));
        when(mockApiManager.getPerformanceStats()).thenAnswer((_) async => {});

        // Act
        final result = await healthMonitor.performHealthCheck();

        // Assert
        expect(result.status, HealthStatus.unhealthy);
      });

      test('determines degraded status for minor issues', () async {
        // Arrange
        when(mockApiManager.isOllamaAvailable()).thenAnswer((_) async => true);
        when(mockApiManager.getAvailableModels()).thenAnswer((_) async => []);
        when(mockErrorTracker.getErrorStats(period: anyNamed('period')))
            .thenAnswer((_) async => ErrorStats(
              totalErrors: 10,
              errorsBySeverity: {ErrorSeverity.medium: 10},
              errorsByCategory: {},
              errorsByOperation: {},
              errorRate: 6.0, // Slightly elevated
              avgResponseTime: Duration.zero,
              topErrors: [],
            ));
        when(mockApiManager.getPerformanceStats()).thenAnswer((_) async => {});

        // Act
        final result = await healthMonitor.performHealthCheck();

        // Assert
        expect(result.status, HealthStatus.degraded);
      });
    });

    group('Performance Integration Tests', () {
      test('includes cache metrics in health check', () async {
        // Arrange
        when(mockApiManager.isOllamaAvailable()).thenAnswer((_) async => true);
        when(mockApiManager.getAvailableModels()).thenAnswer((_) async => []);
        when(mockErrorTracker.getErrorStats(period: anyNamed('period')))
            .thenAnswer((_) async => ErrorStats(
              totalErrors: 0,
              errorsBySeverity: {},
              errorsByCategory: {},
              errorsByOperation: {},
              errorRate: 0.0,
              avgResponseTime: Duration.zero,
              topErrors: [],
            ));
        when(mockApiManager.getPerformanceStats()).thenAnswer((_) async => {
          'cache': {
            'memoryCacheSize': 25,
            'persistentCacheSize': 100,
          },
        });

        // Act
        final result = await healthMonitor.performHealthCheck();

        // Assert
        expect(result.metrics['cache_size'], 25);
        expect(result.metrics['cache_hits'], 100);
      });

      test('includes connection pool metrics in health check', () async {
        // Arrange
        when(mockApiManager.isOllamaAvailable()).thenAnswer((_) async => true);
        when(mockApiManager.getAvailableModels()).thenAnswer((_) async => []);
        when(mockErrorTracker.getErrorStats(period: anyNamed('period')))
            .thenAnswer((_) async => ErrorStats(
              totalErrors: 0,
              errorsBySeverity: {},
              errorsByCategory: {},
              errorsByOperation: {},
              errorRate: 0.0,
              avgResponseTime: Duration.zero,
              topErrors: [],
            ));
        when(mockApiManager.getPerformanceStats()).thenAnswer((_) async => {
          'connectionPool': {
            'active_connections': 3,
            'idle_connections': 2,
            'max_connections': 5,
          },
        });

        // Act
        final result = await healthMonitor.performHealthCheck();

        // Assert
        expect(result.metrics['active_connections'], 3);
        expect(result.metrics['idle_connections'], 2);
      });

      test('detects connection pool exhaustion', () async {
        // Arrange
        when(mockApiManager.isOllamaAvailable()).thenAnswer((_) async => true);
        when(mockApiManager.getAvailableModels()).thenAnswer((_) async => []);
        when(mockErrorTracker.getErrorStats(period: anyNamed('period')))
            .thenAnswer((_) async => ErrorStats(
              totalErrors: 0,
              errorsBySeverity: {},
              errorsByCategory: {},
              errorsByOperation: {},
              errorRate: 0.0,
              avgResponseTime: Duration.zero,
              topErrors: [],
            ));
        when(mockApiManager.getPerformanceStats()).thenAnswer((_) async => {
          'connectionPool': {
            'active_connections': 5,
            'max_connections': 5,
          },
        });

        // Act
        final result = await healthMonitor.performHealthCheck();

        // Assert
        expect(result.issues, contains('Connection pool exhausted'));
      });
    });
  });

  group('HealthCheckResult Tests', () {
    test('creates health check result with all properties', () {
      final timestamp = DateTime.now();
      final responseTime = const Duration(milliseconds: 150);
      final metrics = {'test_metric': 42};
      final issues = ['Issue 1', 'Issue 2'];

      final result = HealthCheckResult(
        status: HealthStatus.degraded,
        timestamp: timestamp,
        responseTime: responseTime,
        metrics: metrics,
        issues: issues,
        statusMessage: 'Test status message',
      );

      expect(result.status, HealthStatus.degraded);
      expect(result.timestamp, timestamp);
      expect(result.responseTime, responseTime);
      expect(result.metrics, metrics);
      expect(result.issues, issues);
      expect(result.statusMessage, 'Test status message');
      expect(result.isDegraded, isTrue);
      expect(result.isHealthy, isFalse);
      expect(result.isUnhealthy, isFalse);
      expect(result.isCritical, isFalse);
    });

    test('status boolean getters work correctly', () {
      final healthyResult = HealthCheckResult(
        status: HealthStatus.healthy,
        timestamp: DateTime.now(),
        responseTime: Duration.zero,
        metrics: {},
        issues: [],
        statusMessage: 'Healthy',
      );

      final criticalResult = HealthCheckResult(
        status: HealthStatus.critical,
        timestamp: DateTime.now(),
        responseTime: Duration.zero,
        metrics: {},
        issues: [],
        statusMessage: 'Critical',
      );

      expect(healthyResult.isHealthy, isTrue);
      expect(healthyResult.isDegraded, isFalse);
      expect(healthyResult.isUnhealthy, isFalse);
      expect(healthyResult.isCritical, isFalse);

      expect(criticalResult.isHealthy, isFalse);
      expect(criticalResult.isDegraded, isFalse);
      expect(criticalResult.isUnhealthy, isFalse);
      expect(criticalResult.isCritical, isTrue);
    });
  });

  group('HealthStatus Tests', () {
    test('HealthStatus enum has correct values', () {
      expect(HealthStatus.values.length, 4);
      expect(HealthStatus.values, contains(HealthStatus.healthy));
      expect(HealthStatus.values, contains(HealthStatus.degraded));
      expect(HealthStatus.values, contains(HealthStatus.unhealthy));
      expect(HealthStatus.values, contains(HealthStatus.critical));
    });

    test('HealthStatus severity ordering is correct', () {
      // Lower index = better health
      expect(HealthStatus.healthy.index < HealthStatus.degraded.index, isTrue);
      expect(HealthStatus.degraded.index < HealthStatus.unhealthy.index, isTrue);
      expect(HealthStatus.unhealthy.index < HealthStatus.critical.index, isTrue);
    });
  });
}