import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
// import 'package:google_fonts/google_fonts.dart'; // Temporarily disabled
import 'core/ollama_api/ollama_api_manager.dart';
import 'core/ollama_api/model_manager.dart';
import 'core/idea_engine/follow_up_generator.dart';
import 'features/brainstorm_chat/presentation/bloc/chat_bloc.dart';
import 'features/brainstorm_chat/presentation/pages/chat_page.dart';
import 'features/home/<USER>/pages/home_page.dart';
import 'features/idea_tree_view/presentation/pages/idea_tree_page.dart';
import 'features/session_management/presentation/bloc/sessions_bloc.dart';
import 'features/session_management/presentation/pages/sessions_page.dart';

import 'features/settings/presentation/pages/model_selection_page.dart';
import 'features/reference_folders/presentation/bloc/reference_folders_bloc.dart';
import 'features/reference_folders/presentation/pages/reference_folders_page.dart';
import 'features/export/presentation/pages/exports_page.dart';
import 'core/session/repositories/session_repository.dart';
import 'core/reference_folders/repositories/reference_folder_repository.dart';
import 'core/templates/repositories/template_repository.dart';
import 'features/collaborative/domain/services/collaborative_service.dart';

void main() {
  runApp(const BrainstormApp());
}

class BrainstormApp extends StatelessWidget {
  const BrainstormApp({super.key});
  
  @override
  Widget build(BuildContext context) {
    return MultiRepositoryProvider(
      providers: [
        RepositoryProvider<OllamaApiManager>(
          create: (_) => OllamaApiManager(), // Use Ollama local AI
        ),
        RepositoryProvider<OllamaModelManager>(
          create: (context) => OllamaModelManager(context.read<OllamaApiManager>()),
        ),
        RepositoryProvider(
          create: (_) => FollowUpGenerator(),
        ),
        RepositoryProvider<SessionRepository>(
          create: (_) => LocalSessionRepository(),
        ),
        RepositoryProvider<ReferenceFolderRepository>(
          create: (_) => ReferenceFolderRepository(),
        ),
        RepositoryProvider<TemplateRepository>(
          create: (_) => AssetTemplateRepository(),
        ),
        RepositoryProvider<CollaborativeService>(
          create: (_) => WebSocketCollaborativeService(),
        ),
      ],
      child: MaterialApp.router(
        title: 'Brainstorm with AI',
        theme: _buildTheme(),
        routerConfig: _router,
      ),
    );
  }
  
  ThemeData _buildTheme() {
    final base = ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFF6750A4),
        brightness: Brightness.light,
      ),
    );
    
    // Use system font for now to avoid network permission issues
    return base;
    // TODO: Re-enable Google Fonts when network permissions are fixed
    // return base.copyWith(
    //   textTheme: GoogleFonts.interTextTheme(base.textTheme),
    // );
  }
}

final _router = GoRouter(
  routes: [
    GoRoute(
      path: '/',
      builder: (context, state) => const HomePage(),
    ),
    GoRoute(
      path: '/sessions',
      builder: (context, state) => BlocProvider(
        create: (context) => SessionsBloc(
          sessionRepository: context.read<SessionRepository>(),
        )..add(LoadSessionsEvent()),
        child: const SessionsPage(),
      ),
    ),
    GoRoute(
      path: '/chat',
      builder: (context, state) {
        final sessionId = state.uri.queryParameters['session'];
        final templateId = state.uri.queryParameters['template'];
        final referenceFolderId = state.uri.queryParameters['referenceFolder'];
        
        return BlocProvider(
          create: (context) => ChatBloc(
            ollamaManager: context.read<OllamaApiManager>(),
            followUpGenerator: context.read<FollowUpGenerator>(),
            sessionRepository: context.read<SessionRepository>(),
            referenceFolderRepository: context.read<ReferenceFolderRepository>(),
            templateRepository: context.read<TemplateRepository>(),
          )..add(InitializeChatEvent(
            sessionId: sessionId,
            templateId: templateId,
            referenceFolderId: referenceFolderId,
          )),
          child: ChatPage(
            sessionId: sessionId,
            templateId: templateId,
          ),
        );
      },
      routes: [
        GoRoute(
          path: 'tree',
          builder: (context, state) {
            // Get the ChatBloc from parent route
            return BlocProvider.value(
              value: context.read<ChatBloc>(),
              child: IdeaTreePage(
                sessionId: state.uri.queryParameters['session'],
              ),
            );
          },
        ),
      ],
    ),

    GoRoute(
      path: '/models',
      builder: (context, state) => const ModelSelectionPage(),
    ),
    GoRoute(
      path: '/reference-folders',
      builder: (context, state) => BlocProvider(
        create: (context) => ReferenceFoldersBloc(
          repository: context.read<ReferenceFolderRepository>(),
        )..add(const LoadReferenceFoldersEvent()),
        child: const ReferenceFoldersPage(),
      ),
    ),
    GoRoute(
      path: '/exports',
      builder: (context, state) => const ExportsPage(),
    ),
  ],
);
