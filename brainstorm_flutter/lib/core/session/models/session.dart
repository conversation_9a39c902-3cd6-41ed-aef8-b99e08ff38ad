import 'package:equatable/equatable.dart';
import '../../idea_engine/idea_tree.dart';

enum SessionStatus {
  active,
  paused,
  completed,
  archived,
}

class Session extends Equatable {
  final String id;
  final String title;
  final String? description;
  final String? templateId;
  final DateTime createdAt;
  final DateTime? lastModified;
  final SessionStatus status;
  final Map<String, dynamic> metadata;
  final String? aiSessionId;
  final IdeaTree? ideaTree;

  const Session({
    required this.id,
    required this.title,
    this.description,
    this.templateId,
    required this.createdAt,
    this.lastModified,
    required this.status,
    this.metadata = const {},
    this.aiSessionId,
    this.ideaTree,
  });

  Session copyWith({
    String? id,
    String? title,
    String? description,
    String? templateId,
    DateTime? createdAt,
    DateTime? lastModified,
    SessionStatus? status,
    Map<String, dynamic>? metadata,
    String? aiSessionId,
    IdeaTree? ideaTree,
  }) {
    return Session(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      templateId: templateId ?? this.templateId,
      createdAt: createdAt ?? this.createdAt,
      lastModified: lastModified ?? this.lastModified,
      status: status ?? this.status,
      metadata: metadata ?? this.metadata,
      aiSessionId: aiSessionId ?? this.aiSessionId,
      ideaTree: ideaTree ?? this.ideaTree,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'templateId': templateId,
      'createdAt': createdAt.toIso8601String(),
      'lastModified': lastModified?.toIso8601String(),
      'status': status.name,
      'metadata': metadata,
      'aiSessionId': aiSessionId,
      'ideaTree': ideaTree?.toJson(),
    };
  }

  factory Session.fromJson(Map<String, dynamic> json) {
    return Session(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      templateId: json['templateId'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastModified: json['lastModified'] != null 
        ? DateTime.parse(json['lastModified'] as String)
        : null,
      status: SessionStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => SessionStatus.active,
      ),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
      aiSessionId: json['aiSessionId'] as String?,
      ideaTree: json['ideaTree'] != null 
        ? IdeaTree.fromJson(json['ideaTree'] as Map<String, dynamic>)
        : null,
    );
  }

  @override
  List<Object?> get props => [
    id, 
    title, 
    description, 
    templateId, 
    createdAt, 
    lastModified, 
    status, 
    metadata, 
    aiSessionId,
    ideaTree,
  ];
}