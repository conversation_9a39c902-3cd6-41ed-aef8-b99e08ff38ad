import 'package:equatable/equatable.dart';
import '../claude_cli/response_parser.dart';
import 'idea_tree.dart';

/// Generates contextual follow-up questions based on AI responses
class FollowUpGenerator {
  
  /// Generate follow-up questions based on response and context
  List<FollowUpQuestion> generateFollowUps({
    required String claudeResponse,
    required String context,
    required IdeaTree ideaTree,
    Idea? currentIdea,
  }) {
    final questions = <FollowUpQuestion>[];

    // Analyze response characteristics
    final hasQuestions = ResponseParser.extractQuestions(claudeResponse).isNotEmpty;
    final keyConcepts = ResponseParser.extractKeyConcepts(claudeResponse);
    final actionItems = ResponseParser.extractActionItems(claudeResponse);
    
    // Generate contextual questions based on response type
    if (hasQuestions) {
      questions.add(_generateExplorationQuestion(keyConcepts));
    }
    
    if (actionItems.isNotEmpty) {
      questions.add(_generateActionQuestion(actionItems));
    }
    
    if (keyConcepts.length >= 2) {
      questions.add(_generateComparisonQuestion(keyConcepts));
    }
    
    // Add depth/breadth question
    questions.add(_generateDepthBreadthQuestion(currentIdea, ideaTree));
    
    // Add perspective question
    questions.add(_generatePerspectiveQuestion(context));
    
    // Limit to 3 questions and ensure variety
    return _selectBestQuestions(questions, 3);
  }
  
  /// Generate exploration question
  FollowUpQuestion _generateExplorationQuestion(List<String> concepts) {
    final concept = concepts.isNotEmpty ? concepts.first : 'this idea';
    
    return FollowUpQuestion(
      text: 'How would you like to explore $concept?',
      choices: [
        Choice(
          'Break it down into components',
          action: FollowUpAction.decompose,
          prompt: 'Let\'s break down $concept into its key components and analyze each one.',
        ),
        Choice(
          'Explore practical applications',
          action: FollowUpAction.practical,
          prompt: 'What are the practical applications and real-world uses of $concept?',
        ),
        Choice(
          'Understand the fundamentals',
          action: FollowUpAction.fundamentals,
          prompt: 'Can you explain the fundamental principles behind $concept?',
        ),
        Choice(
          'Find related concepts',
          action: FollowUpAction.related,
          prompt: 'What concepts are related to $concept and how do they connect?',
        ),
      ],
    );
  }
  
  /// Generate action-oriented question
  FollowUpQuestion _generateActionQuestion(List<String> actionItems) {
    return FollowUpQuestion(
      text: 'What would you like to do next?',
      choices: [
        Choice(
          'Create an action plan',
          action: FollowUpAction.plan,
          prompt: 'Let\'s create a detailed action plan with steps and timeline.',
        ),
        Choice(
          'Prioritize the options',
          action: FollowUpAction.prioritize,
          prompt: 'Help me prioritize these options based on impact and feasibility.',
        ),
        Choice(
          'Analyze challenges',
          action: FollowUpAction.challenges,
          prompt: 'What are the main challenges we might face and how can we overcome them?',
        ),
        Choice(
          'Start with quick wins',
          action: FollowUpAction.quickWins,
          prompt: 'What are the quick wins we can achieve immediately?',
        ),
      ],
    );
  }
  
  /// Generate comparison question
  FollowUpQuestion _generateComparisonQuestion(List<String> concepts) {
    final concept1 = concepts.isNotEmpty ? concepts[0] : 'Option A';
    final concept2 = concepts.length > 1 ? concepts[1] : 'Option B';
    
    return FollowUpQuestion(
      text: 'Would you like to compare or connect ideas?',
      choices: [
        Choice(
          'Compare $concept1 vs $concept2',
          action: FollowUpAction.compare,
          prompt: 'Let\'s compare $concept1 and $concept2 - their pros, cons, and use cases.',
        ),
        Choice(
          'Find connections',
          action: FollowUpAction.connect,
          prompt: 'How do these concepts connect and complement each other?',
        ),
        Choice(
          'Identify patterns',
          action: FollowUpAction.patterns,
          prompt: 'What patterns or themes emerge from these ideas?',
        ),
        Choice(
          'Create synthesis',
          action: FollowUpAction.synthesize,
          prompt: 'Can we synthesize these ideas into a unified approach?',
        ),
      ],
    );
  }
  
  /// Generate depth vs breadth question
  FollowUpQuestion _generateDepthBreadthQuestion(Idea? currentIdea, IdeaTree tree) {
    final hasChildren = currentIdea != null && currentIdea.childIds.isNotEmpty;
    final depth = currentIdea?.depth ?? 0;
    
    return FollowUpQuestion(
      text: 'How should we continue exploring?',
      choices: [
        Choice(
          hasChildren ? 'Go deeper into sub-ideas' : 'Dive deeper',
          action: FollowUpAction.deeper,
          prompt: 'Let\'s explore this concept in more detail and depth.',
        ),
        Choice(
          'Explore alternative approaches',
          action: FollowUpAction.alternatives,
          prompt: 'What are alternative ways to think about or approach this?',
        ),
        Choice(
          depth > 2 ? 'Zoom out for overview' : 'Broaden the scope',
          action: FollowUpAction.broaden,
          prompt: 'Let\'s step back and look at the bigger picture.',
        ),
        Choice(
          'Start a new branch',
          action: FollowUpAction.newBranch,
          prompt: 'Let\'s explore a different aspect or angle of the main topic.',
        ),
      ],
    );
  }
  
  /// Generate perspective question
  FollowUpQuestion _generatePerspectiveQuestion(String context) {
    return FollowUpQuestion(
      text: 'Which perspective would be most helpful?',
      choices: [
        Choice(
          'Technical implementation',
          action: FollowUpAction.technical,
          prompt: 'Let\'s focus on the technical implementation details and requirements.',
        ),
        Choice(
          'User experience',
          action: FollowUpAction.userExperience,
          prompt: 'How would users interact with this? What\'s their journey?',
        ),
        Choice(
          'Business impact',
          action: FollowUpAction.business,
          prompt: 'What\'s the business value and ROI of this approach?',
        ),
        Choice(
          'Creative possibilities',
          action: FollowUpAction.creative,
          prompt: 'Let\'s think creatively - what innovative possibilities does this open up?',
        ),
      ],
    );
  }
  
  /// Select the best questions ensuring variety
  List<FollowUpQuestion> _selectBestQuestions(List<FollowUpQuestion> questions, int count) {
    if (questions.length <= count) return questions;
    
    // Ensure we have diverse question types
    final selected = <FollowUpQuestion>[];
    final used = <String>{};
    
    for (final question in questions) {
      if (selected.length >= count) break;
      
      // Check if we already have a similar question
      final questionType = question.choices.first.action.name;
      if (!used.contains(questionType)) {
        selected.add(question);
        used.add(questionType);
      }
    }
    
    // Fill remaining slots if needed
    for (final question in questions) {
      if (selected.length >= count) break;
      if (!selected.contains(question)) {
        selected.add(question);
      }
    }
    
    return selected;
  }
}

/// A follow-up question with multiple choice options
class FollowUpQuestion extends Equatable {
  final String text;
  final List<Choice> choices;
  final String? id;
  
  FollowUpQuestion({
    required this.text,
    required this.choices,
    String? id,
  }) : id = id ?? DateTime.now().millisecondsSinceEpoch.toString();
  
  @override
  List<Object?> get props => [text, choices, id];
}

/// A choice option for a follow-up question
class Choice extends Equatable {
  final String label;
  final FollowUpAction action;
  final String prompt;
  final Map<String, dynamic>? metadata;
  
  const Choice(
    this.label, {
    required this.action,
    required this.prompt,
    this.metadata,
  });
  
  @override
  List<Object?> get props => [label, action, prompt, metadata];
}

/// Types of follow-up actions
enum FollowUpAction {
  // Exploration
  decompose,
  practical,
  fundamentals,
  related,
  
  // Action
  plan,
  prioritize,
  challenges,
  quickWins,
  
  // Comparison
  compare,
  connect,
  patterns,
  synthesize,
  
  // Depth/Breadth
  deeper,
  alternatives,
  broaden,
  newBranch,
  
  // Perspective
  technical,
  userExperience,
  business,
  creative,
}