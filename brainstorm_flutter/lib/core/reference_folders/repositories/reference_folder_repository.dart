import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:file_picker/file_picker.dart';
import '../models/reference_folder.dart' as models;

/// Repository for managing reference folders
class ReferenceFolderRepository {
  static const String _folderName = 'reference_folders';
  static const String _dataFileName = 'folders.json';
  
  Future<Directory> _getReferenceFoldersDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final refDir = Directory(path.join(appDir.path, _folderName));
    if (!await refDir.exists()) {
      await refDir.create(recursive: true);
    }
    return refDir;
  }
  
  Future<File> _getDataFile() async {
    final dir = await _getReferenceFoldersDirectory();
    return File(path.join(dir.path, _dataFileName));
  }
  
  Future<List<models.ReferenceFolder>> getAllFolders() async {
    try {
      final file = await _getDataFile();
      if (!await file.exists()) {
        return [];
      }
      
      final content = await file.readAsString();
      final List<dynamic> json = jsonDecode(content);
      return json.map((item) => models.ReferenceFolder.fromJson(item)).toList();
    } catch (e) {
      // Error loading reference folders: $e
      return [];
    }
  }
  
  Future<models.ReferenceFolder?> getFolder(String id) async {
    final folders = await getAllFolders();
    try {
      return folders.firstWhere((folder) => folder.id == id);
    } catch (e) {
      return null;
    }
  }
  
  Future<void> saveFolder(models.ReferenceFolder folder) async {
    final folders = await getAllFolders();
    final index = folders.indexWhere((f) => f.id == folder.id);
    
    if (index != -1) {
      folders[index] = folder.copyWith(lastModified: DateTime.now());
    } else {
      folders.add(folder);
    }
    
    await _saveFolders(folders);
  }
  
  Future<void> deleteFolder(String id) async {
    final folders = await getAllFolders();
    folders.removeWhere((folder) => folder.id == id);
    
    // Also delete any copied files
    final dir = await _getReferenceFoldersDirectory();
    final folderDir = Directory(path.join(dir.path, id));
    if (await folderDir.exists()) {
      await folderDir.delete(recursive: true);
    }
    
    await _saveFolders(folders);
  }
  
  Future<void> _saveFolders(List<models.ReferenceFolder> folders) async {
    final file = await _getDataFile();
    final json = folders.map((folder) => folder.toJson()).toList();
    await file.writeAsString(jsonEncode(json));
  }
  
  /// Add files to a reference folder
  Future<models.ReferenceFolder> addFilesToFolder(
    String folderId,
    List<PlatformFile> platformFiles,
  ) async {
    final folder = await getFolder(folderId);
    if (folder == null) {
      throw Exception('Folder not found');
    }
    
    final dir = await _getReferenceFoldersDirectory();
    final folderDir = Directory(path.join(dir.path, folderId));
    if (!await folderDir.exists()) {
      await folderDir.create(recursive: true);
    }
    
    final List<models.ReferenceFile> newFiles = [];
    
    for (final platformFile in platformFiles) {
      if (platformFile.path == null) continue;
      
      final file = File(platformFile.path!);
      final fileName = platformFile.name;
      final destPath = path.join(folderDir.path, fileName);
      
      // Copy file to reference folder directory
      await file.copy(destPath);
      
      // Create reference file entry
      final ext = path.extension(fileName);
      final refFile = models.ReferenceFile.create(
        path: destPath,
        name: fileName,
        type: models.FileType.fromExtension(ext),
        size: platformFile.size,
      );
      
      newFiles.add(refFile);
    }
    
    final updatedFolder = folder.copyWith(
      files: [...folder.files, ...newFiles],
      lastModified: DateTime.now(),
    );
    
    await saveFolder(updatedFolder);
    return updatedFolder;
  }
  
  /// Remove a file from a reference folder
  Future<models.ReferenceFolder> removeFileFromFolder(
    String folderId,
    String fileId,
  ) async {
    final folder = await getFolder(folderId);
    if (folder == null) {
      throw Exception('Folder not found');
    }
    
    final fileToRemove = folder.files.firstWhere(
      (f) => f.id == fileId,
      orElse: () => throw Exception('File not found'),
    );
    
    // Delete the physical file
    final file = File(fileToRemove.path);
    if (await file.exists()) {
      await file.delete();
    }
    
    // Update folder
    final updatedFolder = folder.copyWith(
      files: folder.files.where((f) => f.id != fileId).toList(),
      lastModified: DateTime.now(),
    );
    
    await saveFolder(updatedFolder);
    return updatedFolder;
  }
  
  /// Get file paths for use with AI context
  Future<List<String>> getFilePathsForFolder(String folderId) async {
    final folder = await getFolder(folderId);
    if (folder == null) return [];
    
    return folder.files.map((f) => f.path).toList();
  }
  
  /// Clean up orphaned files
  Future<void> cleanupOrphanedFiles() async {
    final folders = await getAllFolders();
    final dir = await _getReferenceFoldersDirectory();
    
    final allTrackedFiles = <String>{};
    for (final folder in folders) {
      for (final file in folder.files) {
        allTrackedFiles.add(file.path);
      }
    }
    
    // Check each folder directory
    for (final folder in folders) {
      final folderDir = Directory(path.join(dir.path, folder.id));
      if (await folderDir.exists()) {
        final files = await folderDir.list().toList();
        for (final entity in files) {
          if (entity is File && !allTrackedFiles.contains(entity.path)) {
            await entity.delete();
          }
        }
      }
    }
  }
}