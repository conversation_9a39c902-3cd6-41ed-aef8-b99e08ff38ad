import 'dart:async';
import 'dart:math';
import 'ollama_api_manager.dart';

/// Fallback manager that provides graceful degradation when <PERSON><PERSON><PERSON> is unavailable
class OllamaFallbackManager {
  static const List<String> _fallbackResponses = [
    "I understand you're looking for brainstorming help, but I'm currently unable to connect to the AI server. Here are some creative thinking techniques you can try:\n\n• Mind mapping - Start with your central idea and branch out\n• The 5 Whys technique - Keep asking 'why' to dig deeper\n• SCAMPER method - Substitute, Combine, Adapt, Modify, Put to other uses, Eliminate, Reverse\n• Random word association - Pick a random word and connect it to your topic",
    
    "While I can't access the AI models right now, I can suggest some structured brainstorming approaches:\n\n• Start with 'How might we...' questions\n• Set a timer for 10 minutes and write down every idea, no matter how wild\n• Think about your problem from different perspectives (user, business, technical)\n• Consider what would happen if you had unlimited resources vs. very limited resources",
    
    "I'm currently offline, but here are some brainstorming prompts to get you started:\n\n• What would the ideal solution look like?\n• What assumptions am I making that might not be true?\n• How would someone from a different industry approach this?\n• What would happen if we did the complete opposite?\n• What are three unconventional ways to solve this?",
    
    "Connection to AI services is temporarily unavailable. Try these creative exercises:\n\n• The '6 Thinking Hats' method by Edward de Bono\n• Write down 20 ideas - the first 10 will be obvious, the next 10 more creative\n• Think about your problem as if you were designing for children, then for experts\n• Consider environmental, social, and economic impacts of different approaches",
    
    "I'm unable to connect to the AI right now, but you can still brainstorm effectively:\n\n• Use the 'Yes, and...' improv technique to build on ideas\n• Think about problems that are similar but in different contexts\n• Consider both high-tech and low-tech solutions\n• Ask: What would this look like in 5 years? In 50 years?\n• Break your challenge into smaller, more manageable pieces"
  ];

  static final List<OllamaModel> _fallbackModels = [
    OllamaModel(
      name: 'offline-helper',
      size: 'N/A',
      digest: 'offline',
      modifiedAt: DateTime.now(),
    ),
  ];

  final Random _random = Random();
  final OllamaApiManager _primaryManager;
  
  /// Get the primary manager for external access
  OllamaApiManager get primaryManager => _primaryManager;
  
  OllamaFallbackManager(this._primaryManager);

  /// Check if primary connection is available with timeout
  Future<bool> isPrimaryAvailable({Duration? timeout}) async {
    try {
      return await _primaryManager.isOllamaAvailable()
          .timeout(timeout ?? const Duration(seconds: 3));
    } catch (e) {
      return false;
    }
  }

  /// Send chat request with automatic fallback
  Future<OllamaResponse> sendChatRequestWithFallback({
    String model = 'gemma3:4b',
    required String prompt,
    String? sessionId,
    List<String>? contextFiles,
    Map<String, String>? environment,
    bool stream = false,
    bool enableFallback = true,
  }) async {
    final effectiveSessionId = sessionId ?? 'session_${DateTime.now().millisecondsSinceEpoch}';
    
    // Try primary connection first
    if (await isPrimaryAvailable(timeout: const Duration(seconds: 5))) {
      try {
        return await _primaryManager.sendChatRequest(
          model: model,
          prompt: prompt,
          sessionId: effectiveSessionId,
          contextFiles: contextFiles,
          environment: environment,
          stream: stream,
        );
      } catch (e) {
        if (!enableFallback) rethrow;
        // Continue to fallback
      }
    }
    
    if (!enableFallback) {
      return OllamaResponse(
        success: false,
        error: 'Ollama server is not available and fallback is disabled',
        sessionId: effectiveSessionId,
      );
    }
    
    // Fallback response
    return _generateFallbackResponse(prompt, effectiveSessionId);
  }

  /// Generate contextual fallback response
  OllamaResponse _generateFallbackResponse(String prompt, String sessionId) {
    final lowercasePrompt = prompt.toLowerCase();
    String response;
    
    // Detect specific brainstorming contexts and provide targeted advice
    if (lowercasePrompt.contains('app') || lowercasePrompt.contains('mobile')) {
      response = _getAppBrainstormingTips();
    } else if (lowercasePrompt.contains('business') || lowercasePrompt.contains('startup')) {
      response = _getBusinessBrainstormingTips();
    } else if (lowercasePrompt.contains('design') || lowercasePrompt.contains('ui')) {
      response = _getDesignBrainstormingTips();
    } else if (lowercasePrompt.contains('problem') || lowercasePrompt.contains('solve')) {
      response = _getProblemSolvingTips();
    } else {
      // Use random general response
      response = _fallbackResponses[_random.nextInt(_fallbackResponses.length)];
    }
    
    // Add a note about offline mode
    response += '\\n\\n💡 *This response was generated offline. For AI-powered brainstorming, please ensure Ollama is installed and running.*';
    
    return OllamaResponse(
      success: true,
      content: response,
      sessionId: sessionId,
      metadata: {
        'fallback': true,
        'mode': 'offline',
        'context_detected': _detectContext(prompt),
      },
    );
  }

  String _getAppBrainstormingTips() {
    return "I see you're brainstorming about app development! Here are some focused strategies:\\n\\n"
        "📱 **User-Centered Approach:**\\n"
        "• What problem does your app solve?\\n"
        "• Who is your target user and what's their journey?\\n"
        "• How can you make the solution simpler than existing alternatives?\\n\\n"
        "🎯 **Feature Brainstorming:**\\n"
        "• Start with the core feature - what's the one thing your app must do well?\\n"
        "• Think about onboarding - how do users discover value quickly?\\n"
        "• Consider offline capabilities and performance\\n"
        "• What would make users return daily?\\n\\n"
        "🔄 **Validation Ideas:**\\n"
        "• Create mockups or wireframes to test concepts\\n"
        "• Survey potential users about their current solutions\\n"
        "• Look at app store reviews of similar apps for pain points";
  }

  String _getBusinessBrainstormingTips() {
    return "Business brainstorming requires structured thinking! Try these approaches:\\n\\n"
        "💼 **Market Analysis:**\\n"
        "• Who are your customers and what do they value most?\\n"
        "• What trends are emerging in your industry?\\n"
        "• Where are competitors falling short?\\n\\n"
        "💡 **Value Proposition Canvas:**\\n"
        "• Customer jobs: What tasks are customers trying to complete?\\n"
        "• Pain points: What frustrates them in current solutions?\\n"
        "• Gain creators: How can you make their lives better?\\n\\n"
        "🎯 **Business Model Ideas:**\\n"
        "• Revenue streams: How will you make money?\\n"
        "• Key partnerships: Who could help you succeed?\\n"
        "• Unique advantages: What makes you different?";
  }

  String _getDesignBrainstormingTips() {
    return "Design thinking can unlock creative solutions! Here's a framework:\\n\\n"
        "🎨 **Design Thinking Process:**\\n"
        "• Empathize: Understand your users deeply\\n"
        "• Define: Frame the right problem to solve\\n"
        "• Ideate: Generate lots of ideas without judgment\\n"
        "• Prototype: Build quick, testable versions\\n"
        "• Test: Get feedback and iterate\\n\\n"
        "✨ **Visual Brainstorming:**\\n"
        "• Create mood boards for inspiration\\n"
        "• Sketch rapidly - quantity over quality initially\\n"
        "• Consider accessibility from the start\\n"
        "• Think about emotional responses to your design\\n\\n"
        "🔍 **User Experience Focus:**\\n"
        "• Map out user journeys and pain points\\n"
        "• Consider different contexts of use\\n"
        "• Think mobile-first, then expand";
  }

  String _getProblemSolvingTips() {
    return "Problem-solving requires breaking things down systematically:\\n\\n"
        "🔍 **Problem Definition:**\\n"
        "• Restate the problem in different ways\\n"
        "• Ask 'What if this wasn't actually the problem?'\\n"
        "• Consider who benefits from the current situation\\n\\n"
        "⚡ **Solution Generation:**\\n"
        "• Brainstorm without constraints first\\n"
        "• Then add realistic constraints one by one\\n"
        "• Look for solutions in unrelated industries\\n"
        "• Consider both technological and human solutions\\n\\n"
        "🎯 **Evaluation Framework:**\\n"
        "• Impact vs. effort matrix\\n"
        "• Short-term vs. long-term benefits\\n"
        "• Risk assessment and mitigation\\n"
        "• What would success look like?";
  }

  String _detectContext(String prompt) {
    final lowercasePrompt = prompt.toLowerCase();
    if (lowercasePrompt.contains('app')) return 'app_development';
    if (lowercasePrompt.contains('business')) return 'business';
    if (lowercasePrompt.contains('design')) return 'design';
    if (lowercasePrompt.contains('problem')) return 'problem_solving';
    return 'general';
  }

  /// Get available models with fallback
  Future<List<OllamaModel>> getAvailableModelsWithFallback({
    bool enableFallback = true,
    Duration? timeout,
  }) async {
    if (await isPrimaryAvailable(timeout: timeout)) {
      try {
        final models = await _primaryManager.getAvailableModels();
        if (models.isNotEmpty) return models;
      } catch (e) {
        if (!enableFallback) rethrow;
      }
    }
    
    if (!enableFallback) return [];
    
    // Return fallback models
    return _fallbackModels;
  }

  /// Get installation status with fallback information
  Future<OllamaInstallationStatus> getInstallationStatusWithFallback() async {
    try {
      return await _primaryManager.getInstallationStatus();
    } catch (e) {
      return OllamaInstallationStatus(
        isInstalled: false,
        isRunning: false,
        hasModels: false,
        statusMessage: 'Unable to connect to Ollama server',
        recommendedAction: 'Install Ollama from ollama.com or check if the service is running',
      );
    }
  }

  /// Check if we're currently in fallback mode
  bool get isInFallbackMode => _primaryManager.connectionStatus != OllamaConnectionStatus.connected;

  /// Get human-readable status message
  String get statusMessage {
    switch (_primaryManager.connectionStatus) {
      case OllamaConnectionStatus.connected:
        return 'Connected to Ollama server';
      case OllamaConnectionStatus.retrying:
        return 'Connecting to Ollama server...';
      case OllamaConnectionStatus.failed:
        return 'Ollama server unavailable - using offline mode';
      case OllamaConnectionStatus.disconnected:
        return 'Not connected to Ollama server';
    }
  }
}