import 'dart:async';
import 'dart:convert';
import 'caching/ollama_cache_manager.dart';
import 'performance/request_debouncer.dart';
import 'performance/connection_pool.dart';

/// Response from Ollama API operations
class OllamaResponse {
  final bool success;
  final String? content;
  final String? error;
  final String sessionId;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  OllamaResponse({
    required this.success,
    this.content,
    this.error,
    required this.sessionId,
    DateTime? timestamp,
    this.metadata,
  }) : timestamp = timestamp ?? DateTime.now();

  @override
  String toString() {
    return 'OllamaResponse(success: $success, sessionId: $sessionId, '
        'content: ${content?.substring(0, content!.length.clamp(0, 50))}..., '
        'error: $error)';
  }
}

/// Installation status information for Ollama
class OllamaInstallationStatus {
  final bool isInstalled;
  final bool isRunning;
  final bool hasModels;
  final String? version;
  final int? modelCount;
  final List<OllamaModel>? availableModels;
  final String statusMessage;
  final String recommendedAction;

  const OllamaInstallationStatus({
    required this.isInstalled,
    required this.isRunning,
    required this.hasModels,
    this.version,
    this.modelCount,
    this.availableModels,
    required this.statusMessage,
    required this.recommendedAction,
  });

  bool get isFullyConfigured => isInstalled && isRunning && hasModels;
}

/// Connection status for Ollama server
enum OllamaConnectionStatus {
  connected,
  disconnected,
  retrying,
  failed,
}

/// Message structure for Ollama chat API
class OllamaMessage {
  final String role; // 'user', 'assistant', 'system'
  final String content;
  final DateTime timestamp;

  OllamaMessage({
    required this.role,
    required this.content,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  Map<String, dynamic> toJson() {
    return {
      'role': role,
      'content': content,
    };
  }

  factory OllamaMessage.fromJson(Map<String, dynamic> json) {
    return OllamaMessage(
      role: json['role'] as String,
      content: json['content'] as String,
    );
  }

  factory OllamaMessage.user(String content) {
    return OllamaMessage(role: 'user', content: content);
  }

  factory OllamaMessage.assistant(String content) {
    return OllamaMessage(role: 'assistant', content: content);
  }

  factory OllamaMessage.system(String content) {
    return OllamaMessage(role: 'system', content: content);
  }

  @override
  String toString() {
    return 'OllamaMessage(role: $role, content: ${content.substring(0, content.length.clamp(0, 30))}...)';
  }
}

/// Available Ollama model information
class OllamaModel {
  final String name;
  final String size;
  final String digest;
  final DateTime modifiedAt;
  final Map<String, dynamic>? details;

  const OllamaModel({
    required this.name,
    required this.size,
    required this.digest,
    required this.modifiedAt,
    this.details,
  });

  factory OllamaModel.fromJson(Map<String, dynamic> json) {
    return OllamaModel(
      name: json['name'] as String,
      size: json['size']?.toString() ?? 'Unknown',
      digest: json['digest'] as String? ?? '',
      modifiedAt: DateTime.tryParse(json['modified_at'] as String? ?? '') ?? DateTime.now(),
      details: json['details'] as Map<String, dynamic>?,
    );
  }

  /// Returns true if this model is recommended for brainstorming
  bool get isRecommendedForBrainstorming {
    final lowercaseName = name.toLowerCase();
    return lowercaseName.contains('llama') && 
           (lowercaseName.contains('3.2') || lowercaseName.contains('3b')) ||
           lowercaseName.contains('gemma') ||
           lowercaseName.contains('mistral');
  }

  /// Returns a user-friendly description of the model
  String get description {
    final lowercaseName = name.toLowerCase();
    if (lowercaseName.contains('llama') && lowercaseName.contains('3.2')) {
      return 'Fast and efficient, perfect for quick brainstorming';
    } else if (lowercaseName.contains('gemma')) {
      return 'Google\'s creative AI, excellent for innovative thinking';
    } else if (lowercaseName.contains('mistral')) {
      return 'Balanced performance and creativity';
    } else if (lowercaseName.contains('deepseek')) {
      return 'Advanced reasoning capabilities';
    }
    return 'General-purpose language model';
  }

  @override
  String toString() {
    return 'OllamaModel(name: $name, size: $size)';
  }
}

/// Ollama API Manager - Replaces ClaudeProcessManager with HTTP-based integration
class OllamaApiManager {
  static const String defaultBaseUrl = 'http://localhost:11434';
  static const Duration defaultTimeout = Duration(seconds: 30);
  static const String defaultModel = 'gemma2:2b';
  static const int defaultMaxRetries = 3;
  static const Duration defaultRetryDelay = Duration(seconds: 2);

  final String baseUrl;
  final Duration timeout;
  final int maxRetries;
  final Duration retryDelay;
  final Map<String, List<OllamaMessage>> _sessionHistory = {};
  
  // Performance optimization components
  late final OllamaCacheManager _cacheManager;
  late final OllamaConnectionPool _connectionPool;
  late final RequestDebouncer _modelListDebouncer;
  late final RequestThrottler _connectionThrottler;
  
  OllamaConnectionStatus _connectionStatus = OllamaConnectionStatus.disconnected;
  DateTime? _lastConnectionAttempt;
  String? _lastError;

  OllamaApiManager({
    String? baseUrl,
    Duration? timeout,
    int? maxRetries,
    Duration? retryDelay,
  }) : baseUrl = baseUrl ?? defaultBaseUrl,
       timeout = timeout ?? defaultTimeout,
       maxRetries = maxRetries ?? defaultMaxRetries,
       retryDelay = retryDelay ?? defaultRetryDelay {
    // Initialize performance components
    _cacheManager = OllamaCacheManager();
    _connectionPool = OllamaConnectionPool();
    _modelListDebouncer = RequestDebouncer(delay: const Duration(milliseconds: 300));
    _connectionThrottler = RequestThrottler(cooldown: const Duration(seconds: 1));
    
    // Initialize cache
    _cacheManager.initialize();
  }

  /// Connection status getters
  OllamaConnectionStatus get connectionStatus => _connectionStatus;
  DateTime? get lastConnectionAttempt => _lastConnectionAttempt;
  String? get lastError => _lastError;
  
  /// Check if connection should be retried based on last attempt time
  bool get shouldRetryConnection {
    if (_lastConnectionAttempt == null) return true;
    final timeSinceLastAttempt = DateTime.now().difference(_lastConnectionAttempt!);
    return timeSinceLastAttempt > retryDelay;
  }

  /// Execute HTTP request with retry logic and connection status tracking
  Future<T> _executeWithRetry<T>(
    Future<T> Function() operation, {
    T? fallbackValue,
    bool updateConnectionStatus = true,
  }) async {
    if (updateConnectionStatus) {
      _connectionStatus = OllamaConnectionStatus.retrying;
      _lastConnectionAttempt = DateTime.now();
      await _cacheManager.cacheConnectionStatus(_connectionStatus);
    }
    
    Exception? lastException;
    
    for (int attempt = 0; attempt < maxRetries; attempt++) {
      try {
        final result = await operation();
        
        if (updateConnectionStatus) {
          _connectionStatus = OllamaConnectionStatus.connected;
          _lastError = null;
          await _cacheManager.cacheConnectionStatus(_connectionStatus);
        }
        
        return result;
      } catch (e) {
        lastException = e is Exception ? e : Exception(e.toString());
        
        if (attempt < maxRetries - 1) {
          // Exponential backoff: 2s, 4s, 8s, etc.
          final delay = Duration(
            milliseconds: (retryDelay.inMilliseconds * (1 << attempt)).clamp(
              retryDelay.inMilliseconds,
              30000, // Max 30 seconds
            ),
          );
          await Future.delayed(delay);
        }
      }
    }
    
    // All retries failed
    if (updateConnectionStatus) {
      _connectionStatus = OllamaConnectionStatus.failed;
      _lastError = lastException?.toString();
      await _cacheManager.cacheConnectionStatus(_connectionStatus);
    }
    
    if (fallbackValue != null) {
      return fallbackValue;
    }
    
    throw lastException ?? Exception('Unknown error occurred');
  }

  /// Check if Ollama server is available and running
  Future<bool> isOllamaAvailable() async {
    // Check cache first for recent status
    final cachedStatus = await _cacheManager.getCachedConnectionStatus();
    if (cachedStatus == OllamaConnectionStatus.connected) {
      return true;
    }
    
    // Use throttler to prevent excessive connection checks
    final result = await _connectionThrottler.throttleAsync(() async {
      return await _executeWithRetry<bool>(
        () async {
          final client = _connectionPool.getClient();
          try {
            final response = await client
                .get(Uri.parse('$baseUrl/api/tags'))
                .timeout(const Duration(seconds: 5));
            return response.statusCode == 200;
          } finally {
            _connectionPool.returnClient(client);
          }
        },
        fallbackValue: false,
      );
    });
    
    return result ?? false;
  }

  /// Get Ollama version information
  Future<String?> getOllamaVersion() async {
    return await _executeWithRetry<String?>(
      () async {
        final client = _connectionPool.getClient();
        try {
          final response = await client
              .get(Uri.parse('$baseUrl/api/version'))
              .timeout(const Duration(seconds: 5));
          
          if (response.statusCode == 200) {
            final data = jsonDecode(response.body);
            return data['version'] as String?;
          }
          return null;
        } finally {
          _connectionPool.returnClient(client);
        }
      },
      fallbackValue: null,
      updateConnectionStatus: false, // Don't update status for version check
    );
  }

  /// Get list of available models with caching
  Future<List<OllamaModel>> getAvailableModels() async {
    // Check cache first
    final cachedModels = await _cacheManager.getCachedModels();
    if (cachedModels != null) {
      return cachedModels;
    }
    
    try {
      final client = _connectionPool.getClient();
      try {
        final response = await client
            .get(Uri.parse('$baseUrl/api/tags'))
            .timeout(timeout);

        if (response.statusCode == 200) {
          final data = jsonDecode(response.body);
          final models = data['models'] as List<dynamic>? ?? [];
          final modelList = models
              .map((modelJson) => OllamaModel.fromJson(modelJson as Map<String, dynamic>))
              .toList();
          
          // Cache the results
          await _cacheManager.cacheModels(modelList);
          
          return modelList;
        }
        return [];
      } finally {
        _connectionPool.returnClient(client);
      }
    } catch (e) {
      return [];
    }
  }

  /// Send chat request to Ollama API with caching and performance optimizations
  Future<OllamaResponse> sendChatRequest({
    required String model,
    required String prompt,
    String? sessionId,
    List<String>? contextFiles,
    Map<String, String>? environment,
    bool stream = false,
    bool enableCaching = true,
  }) async {
    final uniqueSessionId = sessionId ?? 'ollama_${DateTime.now().millisecondsSinceEpoch}';
    
    // Check cache for similar requests (non-session based only)
    if (enableCaching && sessionId == null) {
      final cacheKey = _cacheManager.generateResponseCacheKey(model, prompt);
      final cachedResponse = await _cacheManager.getCachedResponse(cacheKey);
      if (cachedResponse != null) {
        return cachedResponse;
      }
    }
    
    try {
      // Get or create session history
      final history = _sessionHistory[uniqueSessionId] ?? <OllamaMessage>[];
      
      // Add user message to history
      final userMessage = OllamaMessage.user(prompt);
      history.add(userMessage);
      
      // Prepare request body
      final requestBody = {
        'model': model,
        'messages': history.map((msg) => msg.toJson()).toList(),
        'stream': stream,
        'options': {
          'temperature': 0.7,
          'top_p': 0.9,
          'max_tokens': 2048,
        },
      };

      // Add context files as system message if provided
      if (contextFiles != null && contextFiles.isNotEmpty) {
        final contextContent = 'Context files: ${contextFiles.join(', ')}';
        final systemMessage = OllamaMessage.system(contextContent);
        requestBody['messages'] = [systemMessage.toJson(), ...history.map((msg) => msg.toJson())];
      }

      final client = _connectionPool.getClient();
      try {
        final response = await client.post(
          Uri.parse('$baseUrl/api/chat'),
          headers: {'Content-Type': 'application/json'},
          body: jsonEncode(requestBody),
        ).timeout(timeout);

        if (response.statusCode == 200) {
          final data = jsonDecode(response.body);
          final assistantContent = data['message']?['content'] as String? ?? '';
          
          // Add assistant response to history
          final assistantMessage = OllamaMessage.assistant(assistantContent);
          history.add(assistantMessage);
          _sessionHistory[uniqueSessionId] = history;

          final ollamaResponse = OllamaResponse(
            success: true,
            content: assistantContent,
            sessionId: uniqueSessionId,
            metadata: {
              'model': model,
              'total_duration': data['total_duration'],
              'load_duration': data['load_duration'],
              'prompt_eval_count': data['prompt_eval_count'],
              'eval_count': data['eval_count'],
              'cached': false,
            },
          );
          
          // Cache successful responses (non-session based only)
          if (enableCaching && sessionId == null) {
            final cacheKey = _cacheManager.generateResponseCacheKey(model, prompt);
            await _cacheManager.cacheResponse(cacheKey, ollamaResponse);
          }
          
          return ollamaResponse;
        } else {
          return OllamaResponse(
            success: false,
            error: 'HTTP ${response.statusCode}: ${response.body}',
            sessionId: uniqueSessionId,
          );
        }
      } finally {
        _connectionPool.returnClient(client);
      }
    } catch (e) {
      return OllamaResponse(
        success: false,
        error: 'Connection error: ${e.toString()}',
        sessionId: uniqueSessionId,
      );
    }
  }

  /// Compatibility method to match ClaudeProcessManager interface
  Future<OllamaResponse> sendToClaudeCLI({
    required String prompt,
    String? sessionId,
    List<String>? contextFiles,
    Map<String, String>? environment,
  }) async {
    return await sendChatRequest(
      model: defaultModel,
      prompt: prompt,
      sessionId: sessionId,
      contextFiles: contextFiles,
      environment: environment,
    );
  }

  /// Compatibility method - equivalent to isClaudeAvailable
  Future<bool> isClaudeAvailable() async {
    return await isOllamaAvailable();
  }

  /// Compatibility method - equivalent to getClaudeVersion
  Future<String?> getClaudeVersion() async {
    final version = await getOllamaVersion();
    return version != null ? 'ollama-$version' : null;
  }

  /// Start interactive session (maintains conversation history)
  Future<String?> startInteractiveSession({
    required String sessionId,
    List<String>? contextFiles,
  }) async {
    if (!await isOllamaAvailable()) {
      return null;
    }
    
    // Initialize session history
    _sessionHistory[sessionId] = <OllamaMessage>[];
    
    // Add context files as system message if provided
    if (contextFiles != null && contextFiles.isNotEmpty) {
      final contextContent = 'Context files loaded: ${contextFiles.join(', ')}';
      final systemMessage = OllamaMessage.system(contextContent);
      _sessionHistory[sessionId]!.add(systemMessage);
    }
    
    return sessionId;
  }

  /// Send message to interactive session
  Future<bool> sendToInteractiveSession(String sessionId, String message) async {
    if (!await isOllamaAvailable() || !_sessionHistory.containsKey(sessionId)) {
      return false;
    }
    
    final response = await sendChatRequest(
      model: defaultModel,
      prompt: message,
      sessionId: sessionId,
    );
    
    return response.success;
  }

  /// Close specific session
  Future<void> closeSession(String sessionId) async {
    _sessionHistory.remove(sessionId);
  }

  /// Close all sessions
  Future<void> closeAllSessions() async {
    _sessionHistory.clear();
  }

  /// Get session history for debugging or persistence
  List<OllamaMessage> getSessionHistory(String sessionId) {
    return _sessionHistory[sessionId] ?? [];
  }

  /// Check if a specific model is available
  Future<bool> isModelAvailable(String modelName) async {
    final models = await getAvailableModels();
    return models.any((model) => model.name == modelName);
  }

  /// Get recommended models for brainstorming
  Future<List<OllamaModel>> getRecommendedModels() async {
    final models = await getAvailableModels();
    return models.where((model) => model.isRecommendedForBrainstorming).toList();
  }

  /// Pull/download a model if not available
  Future<bool> pullModel(String modelName) async {
    try {
      final client = _connectionPool.getClient();
      try {
        final response = await client.post(
          Uri.parse('$baseUrl/api/pull'),
          headers: {'Content-Type': 'application/json'},
          body: jsonEncode({'name': modelName}),
        ).timeout(const Duration(minutes: 10)); // Model downloads can take time

        return response.statusCode == 200;
      } finally {
        _connectionPool.returnClient(client);
      }
    } catch (e) {
      return false;
    }
  }

  /// Get server information
  Future<Map<String, dynamic>?> getServerInfo() async {
    try {
      final client = _connectionPool.getClient();
      try {
        final response = await client
            .get(Uri.parse('$baseUrl/api/ps'))
            .timeout(timeout);
            
        if (response.statusCode == 200) {
          return jsonDecode(response.body) as Map<String, dynamic>?;
        }
        return null;
      } finally {
        _connectionPool.returnClient(client);
      }
    } catch (e) {
      return null;
    }
  }

  /// Get detailed installation status information
  Future<OllamaInstallationStatus> getInstallationStatus() async {
    try {
      // Check if server is reachable
      final isAvailable = await isOllamaAvailable();
      if (!isAvailable) {
        return OllamaInstallationStatus(
          isInstalled: false,
          isRunning: false,
          hasModels: false,
          statusMessage: 'Ollama server is not running on localhost:11434',
          recommendedAction: 'Install and start Ollama from ollama.com',
        );
      }

      // Check version
      final version = await getOllamaVersion();
      
      // Check available models
      final models = await getAvailableModels();
      final hasModels = models.isNotEmpty;
      
      String statusMessage;
      String recommendedAction;
      
      if (hasModels) {
        statusMessage = 'Ollama is running with ${models.length} model(s) available';
        recommendedAction = 'Ready to use - select a model to start brainstorming';
      } else {
        statusMessage = 'Ollama is running but no models are installed';
        recommendedAction = 'Pull a model (e.g., "ollama pull gemma2:2b")';
      }
      
      return OllamaInstallationStatus(
        isInstalled: true,
        isRunning: true,
        hasModels: hasModels,
        version: version,
        modelCount: models.length,
        availableModels: models,
        statusMessage: statusMessage,
        recommendedAction: recommendedAction,
      );
    } catch (e) {
      return OllamaInstallationStatus(
        isInstalled: false,
        isRunning: false,
        hasModels: false,
        statusMessage: 'Error checking Ollama status: ${e.toString()}',
        recommendedAction: 'Check if Ollama is installed and running',
      );
    }
  }

  /// Get performance statistics
  Future<Map<String, dynamic>> getPerformanceStats() async {
    final cacheStats = await _cacheManager.getCacheStats();
    final connectionStats = _connectionPool.getStats();
    
    return {
      'cache': cacheStats,
      'connectionPool': connectionStats,
      'sessionHistory': {
        'activeSessions': _sessionHistory.length,
        'totalMessages': _sessionHistory.values.fold<int>(0, (sum, history) => sum + history.length),
      },
      'throttler': {
        'canCall': _connectionThrottler.canCall(),
        'timeUntilNextCall': _connectionThrottler.timeUntilNextCall.inMilliseconds,
      },
    };
  }
  
  /// Clear all caches and reset performance optimizations
  Future<void> clearCache() async {
    await _cacheManager.clearAllCache();
  }
  
  /// Clean expired cache entries
  Future<void> cleanExpiredCache() async {
    await _cacheManager.cleanExpiredCache();
  }
  
  /// Dispose of resources
  void dispose() {
    _connectionPool.dispose();
    _cacheManager.dispose();
    _modelListDebouncer.dispose();
    _sessionHistory.clear();
  }
}