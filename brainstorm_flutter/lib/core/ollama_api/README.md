# Ollama API Integration

This directory contains the complete Ollama integration that replaces Claude CLI with local AI functionality for the Brainstorm Flutter application.

## Overview

The Ollama integration provides a seamless replacement for Claude CLI, offering:
- Local AI inference with privacy and no API costs
- Full compatibility with existing Claude CLI interfaces
- Enhanced performance with caching and connection pooling
- Comprehensive error tracking and health monitoring
- Production-ready test suite

## Architecture

### Core Components

1. **OllamaApiManager** (`ollama_api_manager.dart`)
   - Main integration point for all Ollama functionality
   - HTTP-based communication with Ollama server
   - Session management and conversation history
   - Backward compatibility with Claude CLI methods

2. **OllamaModelManager** (`model_manager.dart`)
   - Model discovery and availability checking
   - Model recommendations based on system capabilities
   - Model pulling and management utilities

3. **Performance Optimization** (`performance/`)
   - **OllamaCacheManager**: Dual-layer caching (memory + persistent)
   - **RequestDebouncer**: Prevents excessive API calls
   - **RequestThrottler**: Rate limiting for stability
   - **OllamaConnectionPool**: HTTP connection pooling

4. **Monitoring & Health** (`monitoring/`)
   - **OllamaErrorTracker**: Comprehensive error tracking and categorization
   - **OllamaHealthMonitor**: System health checks and trend analysis

5. **UI Components** (`../../features/settings/presentation/widgets/`)
   - **PerformanceStatsCard**: Real-time performance metrics display
   - **HealthMonitoringCard**: System health visualization

## Quick Start

### Prerequisites

1. Install Ollama from [ollama.com](https://ollama.com)
2. Start Ollama server: `ollama serve`
3. Pull a model: `ollama pull llama3.2:3b`

### Basic Usage

```dart
// Get the OllamaApiManager instance (provided by dependency injection)
final apiManager = context.read<OllamaApiManager>();

// Check if Ollama is available
final isAvailable = await apiManager.isOllamaAvailable();

// Send a chat request
final response = await apiManager.sendChatRequest(
  model: 'llama3.2:3b',
  prompt: 'Help me brainstorm ideas for a mobile app',
);

if (response.success) {
  print(response.content);
}
```

### Backward Compatibility

The integration maintains full compatibility with Claude CLI interfaces:

```dart
// These methods work seamlessly
final isAvailable = await apiManager.isClaudeAvailable();
final version = await apiManager.getClaudeVersion();
final response = await apiManager.sendToClaudeCLI(
  prompt: 'Your prompt here',
);
```

## Features

### 1. Model Management
- Automatic model discovery
- Smart model recommendations based on system resources
- Model availability checking
- Support for pulling new models

### 2. Performance Optimization
- Response caching with configurable TTL
- Connection pooling (max 5 concurrent connections)
- Request debouncing (500ms default)
- Request throttling (10 requests/minute)
- Efficient memory management

### 3. Error Handling
- Automatic retry with exponential backoff
- Comprehensive error categorization
- Error persistence and statistics
- Graceful degradation

### 4. Health Monitoring
- Real-time health checks
- Performance metrics tracking
- Uptime and stability monitoring
- Trend analysis over time

### 5. Session Management
- Interactive session support
- Conversation history tracking
- Context preservation
- Session cleanup utilities

## Configuration

### Environment Variables

The integration uses sensible defaults but can be configured:

```dart
// Custom base URL (default: http://localhost:11434)
final apiManager = OllamaApiManager(baseUrl: 'http://custom-host:11434');
```

### Performance Tuning

```dart
// Cache configuration
OllamaCacheManager.modelsCacheDuration = Duration(minutes: 10);
OllamaCacheManager.responseCacheDuration = Duration(minutes: 30);

// Connection pool
OllamaConnectionPool.maxConnections = 10;

// Throttling
RequestThrottler(requestsPerMinute: 20);
```

## Testing

The integration includes a comprehensive test suite:

### Unit Tests
```bash
flutter test test/unit/ollama_api_manager_test.dart
flutter test test/unit/error_tracking_test.dart
flutter test test/unit/health_monitoring_test.dart
```

### Widget Tests
```bash
flutter test test/widget/settings_page_widget_test.dart
```

### Integration Tests
```bash
# Requires running Ollama server
flutter test integration_test/ollama_integration_test.dart
```

## Monitoring

### Performance Metrics
Access real-time performance metrics:

```dart
final stats = await apiManager.getPerformanceStats();
// Returns cache stats, connection pool info, session data, etc.
```

### Health Status
Monitor system health:

```dart
final health = healthMonitor.currentHealth;
if (health.status == HealthStatus.degraded) {
  // Handle degraded performance
}
```

### Error Tracking
Track and analyze errors:

```dart
final errorStats = await errorTracker.getErrorStats(
  period: Duration(hours: 1),
);
```

## Migration from Claude CLI

### Step 1: Update Dependencies
The app already includes all necessary dependencies in `pubspec.yaml`.

### Step 2: Replace Process Manager
The dependency injection is already configured to use `OllamaApiManager`.

### Step 3: Update UI References
The test page and other UI components are already updated.

### Step 4: Test
Run the test suite to ensure everything works correctly.

## Troubleshooting

### Common Issues

1. **"Ollama server not found"**
   - Ensure Ollama is installed and running
   - Check server is accessible at http://localhost:11434
   - Verify firewall settings

2. **"No models available"**
   - Pull at least one model: `ollama pull llama3.2:3b`
   - Check available models: `ollama list`

3. **"Connection timeout"**
   - Verify Ollama server is responding
   - Check network connectivity
   - Review connection pool settings

4. **"High memory usage"**
   - Clear cache: `apiManager.clearCache()`
   - Reduce cache duration settings
   - Monitor with performance stats

### Debug Mode

Enable detailed logging:

```dart
OllamaApiManager.enableDebugLogging = true;
```

## Performance Considerations

1. **Model Selection**
   - Smaller models (3B-7B) for faster responses
   - Larger models (13B+) for better quality
   - Consider quantized models for memory efficiency

2. **Caching Strategy**
   - Enable caching for repeated queries
   - Clear cache periodically
   - Monitor cache hit rates

3. **Connection Management**
   - Use connection pooling for multiple requests
   - Configure pool size based on usage patterns
   - Monitor active connections

## Security

1. **Local Execution**
   - All AI inference happens locally
   - No data sent to external servers
   - Complete privacy protection

2. **Network Security**
   - HTTP communication (localhost only)
   - Consider SSH tunneling for remote access
   - Implement authentication if exposing server

## Future Enhancements

1. **Streaming Responses**
   - Implement streaming for real-time output
   - Progressive response rendering
   - Better UX for long responses

2. **Multi-Model Support**
   - Parallel model execution
   - Model comparison features
   - Automatic model selection

3. **Advanced Caching**
   - Semantic similarity caching
   - Distributed cache support
   - Cache warming strategies

4. **Enhanced Monitoring**
   - Grafana integration
   - Custom alerting rules
   - Performance dashboards

## Contributing

When contributing to the Ollama integration:

1. Follow existing code patterns
2. Add comprehensive tests
3. Update documentation
4. Consider performance impact
5. Maintain backward compatibility

## License

This integration follows the same license as the Brainstorm Flutter application.