import 'dart:async';
import 'package:flutter/foundation.dart';
import 'ollama_api_manager.dart';
import 'performance/request_debouncer.dart';

/// Model detection and management system for Ollama integration
class OllamaModelManager extends ChangeNotifier {
  final OllamaApiManager _apiManager;
  
  List<OllamaModel> _availableModels = [];
  List<OllamaModel> _recommendedModels = [];
  OllamaModel? _selectedModel;
  String? _defaultModel;
  bool _isLoading = false;
  String? _lastError;
  DateTime? _lastCheck;
  
  // Performance optimization
  RequestDebouncer? _debouncer;
  
  static const Duration _cacheValidDuration = Duration(minutes: 5);
  static const String _fallbackModel = 'gemma3:4b';
  
  OllamaModelManager(this._apiManager);
  
  // Getters
  List<OllamaModel> get availableModels => List.unmodifiable(_availableModels);
  List<OllamaModel> get recommendedModels => List.unmodifiable(_recommendedModels);
  OllamaModel? get selectedModel => _selectedModel;
  String? get defaultModel => _defaultModel;
  bool get isLoading => _isLoading;
  String? get lastError => _lastError;
  bool get hasModels => _availableModels.isNotEmpty;
  bool get hasRecommendedModels => _recommendedModels.isNotEmpty;
  
  /// Check if cache is still valid
  bool get _isCacheValid {
    if (_lastCheck == null) return false;
    return DateTime.now().difference(_lastCheck!) < _cacheValidDuration;
  }
  
  /// Initialize model manager - detects available models
  Future<void> initialize() async {
    if (_isCacheValid && _availableModels.isNotEmpty) {
      return; // Use cached data
    }
    
    await refreshModels();
  }
  
  /// Refresh model list from Ollama server
  Future<void> refreshModels() async {
    _setLoading(true);
    _clearError();
    
    try {
      // Check if Ollama is available
      if (!await _apiManager.isOllamaAvailable()) {
        throw Exception('Ollama server is not available at ${_apiManager.baseUrl}');
      }
      
      // Get available models
      final models = await _apiManager.getAvailableModels();
      _availableModels = models;
      _lastCheck = DateTime.now();
      
      // Filter recommended models
      _recommendedModels = models.where((model) => model.isRecommendedForBrainstorming).toList();
      
      // Set default model if not already set
      _defaultModel ??= _determineDefaultModel();
      
      // Set selected model if not already set
      if (_selectedModel == null && _availableModels.isNotEmpty) {
        _selectedModel = _findModelByName(_defaultModel) ?? _availableModels.first;
      }
      
      debugPrint('OllamaModelManager: Found ${_availableModels.length} models, ${_recommendedModels.length} recommended');
      
    } catch (e) {
      _setError('Failed to load models: ${e.toString()}');
      debugPrint('OllamaModelManager error: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  /// Determine the best default model based on available models
  String _determineDefaultModel() {
    if (_availableModels.isEmpty) return _fallbackModel;
    
    // Priority order for default models
    final preferredModels = [
      'gemma3:4b',
      'llama3.2:1b',
      'llama3.2',
      'gemma2:9b',
      'gemma3:4b',
      'gemma2',
      'mistral:7b',
      'mistral',
    ];
    
    // Find first available preferred model
    for (final preferred in preferredModels) {
      final model = _findModelByName(preferred);
      if (model != null) {
        return model.name;
      }
    }
    
    // Fall back to first recommended model
    if (_recommendedModels.isNotEmpty) {
      return _recommendedModels.first.name;
    }
    
    // Fall back to first available model
    return _availableModels.first.name;
  }
  
  /// Find model by name (case-insensitive, partial match)
  OllamaModel? _findModelByName(String? name) {
    if (name == null) return null;
    
    final lowerName = name.toLowerCase();
    
    // Try exact match first
    for (final model in _availableModels) {
      if (model.name.toLowerCase() == lowerName) {
        return model;
      }
    }
    
    // Try partial match
    for (final model in _availableModels) {
      if (model.name.toLowerCase().contains(lowerName)) {
        return model;
      }
    }
    
    return null;
  }
  
  /// Select a model for use
  Future<bool> selectModel(String modelName) async {
    final model = _findModelByName(modelName);
    if (model == null) {
      _setError('Model "$modelName" not found');
      return false;
    }
    
    // Verify model is available on server
    if (!await _apiManager.isModelAvailable(model.name)) {
      _setError('Model "${model.name}" is not available on server');
      return false;
    }
    
    _selectedModel = model;
    _clearError();
    notifyListeners();
    
    debugPrint('OllamaModelManager: Selected model ${model.name}');
    return true;
  }
  
  /// Auto-select best available model
  Future<bool> autoSelectModel() async {
    if (_availableModels.isEmpty) {
      await refreshModels();
    }
    
    if (_availableModels.isEmpty) {
      _setError('No models available');
      return false;
    }
    
    // Try recommended models first
    if (_recommendedModels.isNotEmpty) {
      return await selectModel(_recommendedModels.first.name);
    }
    
    // Fall back to any available model
    return await selectModel(_availableModels.first.name);
  }
  
  /// Check if a specific model is available and ready
  Future<bool> isModelReady(String modelName) async {
    if (_availableModels.isEmpty) {
      await refreshModels();
    }
    
    final model = _findModelByName(modelName);
    if (model == null) return false;
    
    return await _apiManager.isModelAvailable(model.name);
  }
  
  /// Pull/download a model if not available
  Future<bool> pullModel(String modelName) async {
    _setLoading(true);
    _clearError();
    
    try {
      final success = await _apiManager.pullModel(modelName);
      
      if (success) {
        // Refresh model list after successful pull
        await refreshModels();
        debugPrint('OllamaModelManager: Successfully pulled model $modelName');
      } else {
        _setError('Failed to pull model "$modelName"');
      }
      
      return success;
    } catch (e) {
      _setError('Error pulling model: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }
  
  /// Get model recommendations based on use case
  List<OllamaModel> getModelsForUseCase(String useCase) {
    switch (useCase.toLowerCase()) {
      case 'brainstorming':
      case 'creative':
        return _recommendedModels;
      case 'fast':
      case 'quick':
        return _availableModels
            .where((model) => model.name.toLowerCase().contains('3b') || 
                            model.name.toLowerCase().contains('1b'))
            .toList();
      case 'detailed':
      case 'comprehensive':
        return _availableModels
            .where((model) => model.name.toLowerCase().contains('7b') || 
                            model.name.toLowerCase().contains('9b') ||
                            model.name.toLowerCase().contains('13b'))
            .toList();
      default:
        return _availableModels;
    }
  }
  
  /// Get current model for API calls
  String getCurrentModelName() {
    return _selectedModel?.name ?? _defaultModel ?? _fallbackModel;
  }
  
  /// Get model statistics
  Map<String, dynamic> getModelStats() {
    return {
      'total_models': _availableModels.length,
      'recommended_models': _recommendedModels.length,
      'selected_model': _selectedModel?.name,
      'default_model': _defaultModel,
      'last_check': _lastCheck?.toIso8601String(),
      'cache_valid': _isCacheValid,
      'has_errors': _lastError != null,
    };
  }
  
  /// Debounced refresh to prevent excessive API calls
  void debouncedRefresh() {
    if (_debouncer?.hasPendingCall ?? false) {
      return; // Already has a pending refresh
    }
    
    _debouncer = RequestDebouncer(delay: const Duration(milliseconds: 500));
    _debouncer!.call(() {
      refreshModels();
    });
  }

  /// Clear model cache and force refresh
  Future<void> clearCache() async {
    _availableModels.clear();
    _recommendedModels.clear();
    _lastCheck = null;
    _clearError();
    notifyListeners();
    
    await refreshModels();
  }
  
  @override
  void dispose() {
    _debouncer?.dispose();
    super.dispose();
  }
  
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }
  
  void _setError(String error) {
    _lastError = error;
    notifyListeners();
  }
  
  void _clearError() {
    if (_lastError != null) {
      _lastError = null;
      notifyListeners();
    }
  }
  
}

/// Model selection helper - provides easy access to model management
class ModelSelectionHelper {
  static const Map<String, String> modelCategories = {
    'fast': 'Quick responses (1B-3B parameters)',
    'balanced': 'Good balance of speed and quality (7B-9B parameters)', 
    'comprehensive': 'Best quality responses (13B+ parameters)',
  };
  
  static const Map<String, List<String>> categoryModels = {
    'fast': ['llama3.2:1b', 'gemma3:4b', 'gemma3:4b'],
    'balanced': ['llama3.2:7b', 'gemma2:9b', 'mistral:7b'],
    'comprehensive': ['llama3.2:70b', 'gemma2:27b', 'mistral:8x7b'],
  };
  
  /// Get user-friendly category name for a model
  static String getCategoryForModel(String modelName) {
    final lowerName = modelName.toLowerCase();
    
    if (lowerName.contains('1b') || lowerName.contains('3b')) {
      return 'fast';
    } else if (lowerName.contains('7b') || lowerName.contains('9b')) {
      return 'balanced'; 
    } else if (lowerName.contains('13b') || lowerName.contains('27b') || lowerName.contains('70b')) {
      return 'comprehensive';
    }
    
    return 'balanced'; // Default category
  }
  
  /// Get recommended models for first-time users
  static List<String> getRecommendedForNewUsers() {
    return ['gemma3:4b', 'gemma3:4b', 'mistral:7b'];
  }
}