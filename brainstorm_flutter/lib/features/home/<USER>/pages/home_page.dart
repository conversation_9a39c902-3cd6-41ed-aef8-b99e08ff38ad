import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:flutter_animate/flutter_animate.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(LucideIcons.settings),
            tooltip: 'Settings',
            onSelected: (value) {
              switch (value) {
                case 'models':
                  context.go('/models');
                  break;
                case 'test':
                  context.go('/test-claude');
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'models',
                child: Row(
                  children: [
                    Icon(LucideIcons.cpu),
                    SizedBox(width: 12),
                    Text('AI Models'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'test',
                child: Row(
                  children: [
                    Icon(LucideIcons.testTube),
                    Sized<PERSON>ox(width: 12),
                    Text('Test Ollama'),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(width: 8),
        ],
      ),
      extendBodyBehindAppBar: true,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              theme.colorScheme.primary.withAlpha(25),
              theme.colorScheme.secondary.withAlpha(25),
            ],
          ),
        ),
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 600),
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                    // Logo and title
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primaryContainer,
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        LucideIcons.brain,
                        size: 64,
                        color: theme.colorScheme.onPrimaryContainer,
                      ),
                    ).animate()
                      .scale(duration: 600.ms, curve: Curves.elasticOut),
                    
                    const SizedBox(height: 32),
                    
                    Text(
                      'Brainstorm with AI',
                      style: theme.textTheme.displaySmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                      textAlign: TextAlign.center,
                    ).animate()
                      .fadeIn(delay: 200.ms, duration: 500.ms),
                    
                    const SizedBox(height: 16),
                    
                    Text(
                      'Unlock creative ideas with AI-powered brainstorming',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      textAlign: TextAlign.center,
                    ).animate()
                      .fadeIn(delay: 400.ms, duration: 500.ms),
                    
                    const SizedBox(height: 48),
                    
                    // Action buttons
                    Wrap(
                      alignment: WrapAlignment.center,
                      spacing: 16,
                      runSpacing: 16,
                      children: [
                        FilledButton.icon(
                          onPressed: () => context.go('/chat'),
                          icon: const Icon(LucideIcons.messageSquarePlus),
                          label: const Text('Start New Session'),
                          style: FilledButton.styleFrom(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 24,
                              vertical: 12,
                            ),
                            textStyle: theme.textTheme.titleMedium,
                          ),
                        ).animate()
                          .fadeIn(delay: 600.ms, duration: 500.ms)
                          .slideY(begin: 0.2, duration: 500.ms),
                        
                        OutlinedButton.icon(
                          onPressed: () => context.go('/sessions'),
                          icon: const Icon(LucideIcons.folderOpen),
                          label: const Text('View Sessions'),
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 24,
                              vertical: 12,
                            ),
                            textStyle: theme.textTheme.titleMedium,
                          ),
                        ).animate()
                          .fadeIn(delay: 700.ms, duration: 500.ms)
                          .slideY(begin: 0.2, duration: 500.ms),
                        
                        OutlinedButton.icon(
                          onPressed: () => context.go('/reference-folders'),
                          icon: const Icon(LucideIcons.bookOpen),
                          label: const Text('Reference Folders'),
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 24,
                              vertical: 12,
                            ),
                            textStyle: theme.textTheme.titleMedium,
                          ),
                        ).animate()
                          .fadeIn(delay: 800.ms, duration: 500.ms)
                          .slideY(begin: 0.2, duration: 500.ms),
                      ],
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Template grid
                    Text(
                      'Start with a template:',
                      style: theme.textTheme.titleSmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ).animate()
                      .fadeIn(delay: 900.ms, duration: 500.ms),
                    
                    const SizedBox(height: 16),
                    
                    GridView.count(
                      shrinkWrap: true,
                      crossAxisCount: 2,
                      mainAxisSpacing: 16,
                      crossAxisSpacing: 16,
                      childAspectRatio: 2,
                      children: [
                        _buildTemplateCard(
                          context: context,
                          icon: LucideIcons.gitBranch,
                          title: 'Mind Map',
                          description: 'Branch out ideas',
                          templateId: 'mind_map',
                          delay: 1000,
                        ),
                        _buildTemplateCard(
                          context: context,
                          icon: LucideIcons.target,
                          title: 'SWOT Analysis',
                          description: 'Strategic planning',
                          templateId: 'swot',
                          delay: 1100,
                        ),
                        _buildTemplateCard(
                          context: context,
                          icon: LucideIcons.hardHat,
                          title: 'Six Hats',
                          description: 'Multiple perspectives',
                          templateId: 'six_hats',
                          delay: 1200,
                        ),
                        _buildTemplateCard(
                          context: context,
                          icon: LucideIcons.shuffle,
                          title: 'SCAMPER',
                          description: 'Creative thinking',
                          templateId: 'scamper',
                          delay: 1300,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    ),
  );
  }
  
  Widget _buildTemplateCard({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String description,
    required String templateId,
    required int delay,
  }) {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: theme.colorScheme.outline.withAlpha(50),
        ),
      ),
      child: InkWell(
        onTap: () => context.go('/chat?template=$templateId'),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 28,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: theme.textTheme.labelLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 2),
              Text(
                description,
                style: theme.textTheme.labelSmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    ).animate()
      .fadeIn(delay: Duration(milliseconds: delay), duration: 500.ms)
      .scale(
        delay: Duration(milliseconds: delay),
        duration: 500.ms,
        begin: const Offset(0.8, 0.8),
      );
  }
}