import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:go_router/go_router.dart';
import '../bloc/chat_bloc.dart';
import '../widgets/chat_message_bubble.dart';
import '../widgets/multi_choice_widget.dart';
import '../../../../core/idea_engine/follow_up_generator.dart';
import '../../../../core/export/models/export_format.dart';
import '../../../export/presentation/widgets/export_dialog.dart';
import '../../../export/presentation/widgets/export_progress_dialog.dart';
// TODO: Implement collaborative sessions
// import '../../../collaborative/presentation/pages/collaborative_session_page.dart';
import '../../../voice_input/domain/services/voice_input_service.dart';
import '../../../voice_input/presentation/widgets/voice_input_button.dart';

class ChatPage extends StatefulWidget {
  final String? sessionId;
  final String? templateId;
  
  const ChatPage({
    super.key,
    this.sessionId,
    this.templateId,
  });
  
  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> {
  final TextEditingController _textController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final FocusNode _focusNode = FocusNode();
  late final VoiceInputService _voiceInputService;
  
  @override
  void initState() {
    super.initState();
    _voiceInputService = SpeechToTextVoiceInputService();
  }
  
  @override
  void dispose() {
    _textController.dispose();
    _scrollController.dispose();
    _focusNode.dispose();
    _voiceInputService.dispose();
    super.dispose();
  }
  
  void _sendMessage() {
    final text = _textController.text.trim();
    if (text.isEmpty) return;
    
    context.read<ChatBloc>().add(SendMessageEvent(
      content: text,
      sessionId: widget.sessionId,
    ));
    
    _textController.clear();
    _scrollToBottom();
  }
  
  void _handleFollowUp(Choice choice, FollowUpQuestion question) {
    context.read<ChatBloc>().add(SelectFollowUpEvent(
      choice: choice,
      question: question,
    ));
    _scrollToBottom();
  }
  
  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: theme.colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                LucideIcons.brain,
                size: 20,
                color: theme.colorScheme.onPrimaryContainer,
              ),
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Brainstorm Session',
                  style: theme.textTheme.titleMedium,
                ),
                BlocBuilder<ChatBloc, ChatState>(
                  builder: (context, state) {
                    if (state is ChatLoaded) {
                      return Text(
                        'Session: ${state.sessionId}',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ],
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(LucideIcons.treeDeciduous),
            tooltip: 'View Idea Tree',
            onPressed: () {
              context.go('/chat/tree?session=${widget.sessionId ?? ''}');
            },
          ),
          IconButton(
            icon: const Icon(LucideIcons.users),
            tooltip: 'Collaborative Session',
            onPressed: () {
              // TODO: Implement collaborative sessions
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Collaborative sessions coming soon!'),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(LucideIcons.download),
            tooltip: 'Export Ideas',
            onPressed: () => _showExportDialog(context),
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Column(
        children: [
          // Messages
          Expanded(
            child: BlocBuilder<ChatBloc, ChatState>(
              builder: (context, state) {
                if (state is ChatLoading) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }
                
                if (state is ChatError) {
                  return Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          LucideIcons.alertCircle,
                          size: 48,
                          color: theme.colorScheme.error,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Error: ${state.message}',
                          style: theme.textTheme.bodyLarge?.copyWith(
                            color: theme.colorScheme.error,
                          ),
                        ),
                        const SizedBox(height: 16),
                        FilledButton.tonal(
                          onPressed: () {
                            context.read<ChatBloc>().add(
                              InitializeChatEvent(sessionId: widget.sessionId),
                            );
                          },
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  );
                }
                
                if (state is ChatLoaded) {
                  if (state.messages.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            LucideIcons.messageSquarePlus,
                            size: 64,
                            color: theme.colorScheme.primary.withAlpha(100),
                          ),
                          const SizedBox(height: 24),
                          Text(
                            'Start your brainstorming session',
                            style: theme.textTheme.headlineSmall,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Type your idea or question below',
                            style: theme.textTheme.bodyLarge?.copyWith(
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                          if (widget.templateId != null) ...[
                            const SizedBox(height: 24),
                            FilledButton.icon(
                              onPressed: () {
                                context.read<ChatBloc>().add(
                                  LoadTemplateEvent(templateId: widget.templateId!),
                                );
                              },
                              icon: const Icon(LucideIcons.fileText),
                              label: const Text('Load Template'),
                            ),
                          ],
                        ],
                      ),
                    );
                  }
                  
                  return ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.only(bottom: 16),
                    itemCount: state.messages.length + 
                      (state.lastFollowUpQuestions != null ? 1 : 0),
                    itemBuilder: (context, index) {
                      if (index < state.messages.length) {
                        final message = state.messages[index];
                        return ChatMessageBubble(
                          message: message,
                          onCopyTap: () async {
                            await Clipboard.setData(ClipboardData(text: message.content));
                            if (mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: const Text('Message copied to clipboard'),
                                  duration: const Duration(seconds: 2),
                                  behavior: SnackBarBehavior.floating,
                                  backgroundColor: theme.colorScheme.primary,
                                ),
                              );
                            }
                          },
                          onIdeaExtract: (ideaId) {
                            context.read<ChatBloc>().add(
                              ExtractIdeaEvent(messageId: ideaId),
                            );
                          },
                        );
                      } else if (state.lastFollowUpQuestions != null) {
                        return MultiChoiceWidget(
                          questions: state.lastFollowUpQuestions!,
                          onChoiceSelected: _handleFollowUp,
                          isEnabled: !state.isProcessing,
                        );
                      }
                      return const SizedBox.shrink();
                    },
                  );
                }
                
                return const Center(
                  child: Text('Initializing...'),
                );
              },
            ),
          ),
          
          // Input area
          Container(
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              boxShadow: [
                BoxShadow(
                  color: theme.colorScheme.shadow.withAlpha(25),
                  blurRadius: 8,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: BlocBuilder<ChatBloc, ChatState>(
                      builder: (context, state) {
                        final isProcessing = state is ChatLoaded && state.isProcessing;
                        
                        return TextField(
                          controller: _textController,
                          focusNode: _focusNode,
                          enabled: !isProcessing,
                          maxLines: null,
                          textInputAction: TextInputAction.send,
                          onSubmitted: (_) => _sendMessage(),
                          decoration: InputDecoration(
                            hintText: isProcessing 
                              ? 'AI is thinking...'
                              : 'Share your thoughts...',
                            filled: true,
                            fillColor: theme.colorScheme.surfaceContainerHighest,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(24),
                              borderSide: BorderSide.none,
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 12,
                            ),
                            prefixIcon: Icon(
                              LucideIcons.sparkles,
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  VoiceInputButton(
                    voiceInputService: _voiceInputService,
                    onResult: (text) {
                      setState(() {
                        _textController.text = text;
                      });
                    },
                    onStartListening: () {
                      _focusNode.unfocus();
                    },
                  ),
                  const SizedBox(width: 8),
                  BlocBuilder<ChatBloc, ChatState>(
                    builder: (context, state) {
                      final isProcessing = state is ChatLoaded && state.isProcessing;
                      
                      return AnimatedContainer(
                        duration: const Duration(milliseconds: 200),
                        child: IconButton.filled(
                          onPressed: isProcessing ? null : _sendMessage,
                          icon: AnimatedSwitcher(
                            duration: const Duration(milliseconds: 200),
                            child: isProcessing
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                  ),
                                )
                              : const Icon(LucideIcons.send),
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _showExportDialog(BuildContext context) async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => ExportDialog(
        title: 'Brainstorm Session',
      ),
    );

    if (result != null && mounted) {
      _performExport(
        context,
        result['format'] as ExportFormat,
        result['fileName'] as String,
        result['includeMessages'] as bool,
        result['includeMetadata'] as bool,
      );
    }
  }

  Future<void> _performExport(
    BuildContext context,
    ExportFormat format,
    String fileName,
    bool includeMessages,
    bool includeMetadata,
  ) async {
    // Show progress dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const ExportProgressDialog(
        message: 'Preparing export...',
      ),
    );

    try {
      // Trigger the export
      context.read<ChatBloc>().add(ExportIdeasEvent(
        format: format,
        fileName: fileName,
        includeMessages: includeMessages,
        includeMetadata: includeMetadata,
      ));

      // Wait a moment for the export to complete
      await Future.delayed(const Duration(milliseconds: 500));

      if (mounted) {
        // Close progress dialog
        Navigator.of(context).pop();

        // Show success dialog
        showDialog(
          context: context,
          builder: (context) => ExportProgressDialog(
            message: 'Export completed successfully!\nFile saved to Downloads folder.',
            isComplete: true,
            filePath: '$fileName.${format.extension}',
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        // Close progress dialog
        Navigator.of(context).pop();

        // Show error
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export failed: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }
}