import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../domain/entities/message.dart';
import '../../../../core/ollama_api/ollama_api_manager.dart';
import '../../../../core/claude_cli/response_parser.dart';
import '../../../../core/idea_engine/idea_tree.dart';
import '../../../../core/idea_engine/follow_up_generator.dart';
import '../../../../core/session/models/session.dart';
import '../../../../core/session/repositories/session_repository.dart';
import '../../../../core/reference_folders/repositories/reference_folder_repository.dart';
import '../../../../core/export/services/export_service.dart';
import '../../../../core/export/models/export_data.dart';
import '../../../../core/export/models/export_format.dart';
import '../../../../core/templates/repositories/template_repository.dart';
import '../../../../core/offline/services/offline_service.dart';
import '../../domain/models/chat_message.dart' as chat_models;

part 'chat_event.dart';
part 'chat_state.dart';

class ChatBloc extends Bloc<ChatEvent, ChatState> {
  final OllamaApiManager _ollamaManager;
  final FollowUpGenerator _followUpGenerator;
  final SessionRepository _sessionRepository;
  final ReferenceFolderRepository _referenceFolderRepository;
  final ExportService _exportService;
  final TemplateRepository _templateRepository;
  final OfflineService? _offlineService;
  
  IdeaTree _ideaTree;
  String? _currentSessionId;
  Session? _currentSession;
  Idea? _currentIdea;
  String? _referenceFolderId;
  List<String> _contextFiles = [];
  
  ChatBloc({
    required OllamaApiManager ollamaManager,
    required FollowUpGenerator followUpGenerator,
    required SessionRepository sessionRepository,
    required ReferenceFolderRepository referenceFolderRepository,
    required TemplateRepository templateRepository,
    ExportService? exportService,
    IdeaTree? ideaTree,
    OfflineService? offlineService,
  }) : _ollamaManager = ollamaManager,
       _followUpGenerator = followUpGenerator,
       _sessionRepository = sessionRepository,
       _referenceFolderRepository = referenceFolderRepository,
       _exportService = exportService ?? ExportService(),
       _templateRepository = templateRepository,
       _ideaTree = ideaTree ?? IdeaTree(),
       _offlineService = offlineService,
       super(ChatInitial()) {
    on<InitializeChatEvent>(_onInitialize);
    on<SendMessageEvent>(_onSendMessage);
    on<SelectFollowUpEvent>(_onSelectFollowUp);
    on<ExtractIdeaEvent>(_onExtractIdea);
    on<LoadTemplateEvent>(_onLoadTemplate);
    on<ExportIdeasEvent>(_onExportIdeas);
  }
  
  Future<void> _onInitialize(
    InitializeChatEvent event,
    Emitter<ChatState> emit,
  ) async {
    emit(ChatLoading());
    
    try {
      // Handle reference folder
      if (event.referenceFolderId != null) {
        _referenceFolderId = event.referenceFolderId;
        _contextFiles = await _referenceFolderRepository.getFilePathsForFolder(event.referenceFolderId!);
      }
      
      if (event.sessionId != null) {
        // Load existing session
        _currentSession = await _sessionRepository.getSession(event.sessionId!);
        if (_currentSession != null) {
          _currentSessionId = _currentSession!.id;
          _ideaTree = _currentSession!.ideaTree ?? IdeaTree();
        } else {
          // Session not found, create new one
          _currentSessionId = event.sessionId;
        }
      } else {
        // Create new session
        _currentSessionId = 'brainstorm_${DateTime.now().millisecondsSinceEpoch}';
      }
      
      emit(ChatLoaded(
        messages: [],
        sessionId: _currentSessionId!,
        ideaTree: _ideaTree,
        currentSession: _currentSession,
        referenceFolderId: _referenceFolderId,
        contextFiles: _contextFiles,
      ));
    } catch (e) {
      emit(ChatError(message: e.toString()));
    }
  }
  
  Future<void> _onSendMessage(
    SendMessageEvent event,
    Emitter<ChatState> emit,
  ) async {
    final state = this.state;
    if (state is! ChatLoaded) return;
    
    // Add user message
    final userMessage = Message(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: event.content,
      type: MessageType.user,
      timestamp: DateTime.now(),
      sessionId: _currentSessionId,
    );
    
    emit(state.copyWith(
      messages: [...state.messages, userMessage],
      isProcessing: true,
      lastFollowUpQuestions: null,
    ));
    
    // Cache the user message if offline service is available
    if (_offlineService != null && _currentSessionId != null) {
      final chatMessage = chat_models.ChatMessage(
        id: userMessage.id,
        content: userMessage.content,
        type: chat_models.MessageType.user,
        timestamp: userMessage.timestamp,
        sessionId: _currentSessionId!,
      );
      await _offlineService!.cacheMessage(_currentSessionId!, chatMessage);
    }
    
    // Check if we're offline
    if (_offlineService != null && !_offlineService!.isOnline) {
      // Add to pending messages
      await _offlineService!.addPendingMessage(_currentSessionId!, event.content);
      
      final offlineMessage = Message(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        content: 'Message queued for sending when online',
        type: MessageType.system,
        timestamp: DateTime.now(),
        sessionId: _currentSessionId,
      );
      
      emit(state.copyWith(
        messages: [...state.messages, offlineMessage],
        isProcessing: false,
      ));
      return;
    }
    
    try {
      // Send to Ollama API
      final contextFiles = event.contextFiles ?? _contextFiles;
      final response = await _ollamaManager.sendChatRequest(
        model: OllamaApiManager.defaultModel,
        prompt: event.content,
        sessionId: _currentSessionId,
        contextFiles: contextFiles.isNotEmpty ? contextFiles : null,
      );

      if (response.success && response.content != null) {
        // Parse response and extract ideas
        final parsedResponse = ResponseParser.parseResponse(response.content!);

        // Create assistant's message
        final assistantMessage = Message(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          content: response.content!,
          type: MessageType.assistant,
          timestamp: DateTime.now(),
          sessionId: _currentSessionId,
          metadata: {
            'parsed': parsedResponse,
            'ideas': parsedResponse.ideas.map((i) => i.content).toList(),
          },
        );

        // Add ideas to tree
        if (parsedResponse.ideas.isNotEmpty) {
          for (final idea in parsedResponse.ideas) {
            _ideaTree.addIdea(idea, parentId: _currentIdea?.id);
          }
        }

        // Generate follow-up questions
        final followUpQuestions = _followUpGenerator.generateFollowUps(
          claudeResponse: response.content!,
          context: event.content,
          ideaTree: _ideaTree,
          currentIdea: _currentIdea,
        );
        
        emit(state.copyWith(
          messages: [...state.messages, assistantMessage],
          isProcessing: false,
          lastFollowUpQuestions: followUpQuestions,
          ideaTree: _ideaTree,
        ));
        
        // Cache the assistant message if offline service is available
        if (_offlineService != null && _currentSessionId != null) {
          final chatMessage = chat_models.ChatMessage(
            id: assistantMessage.id,
            content: assistantMessage.content,
            type: chat_models.MessageType.assistant,
            timestamp: assistantMessage.timestamp,
            sessionId: _currentSessionId!,
            metadata: assistantMessage.metadata,
          );
          await _offlineService!.cacheMessage(_currentSessionId!, chatMessage);
        }
        
        // Save session after each successful message
        await _saveCurrentSession();
      } else {
        // Handle error
        final errorMessage = Message(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          content: response.error ?? 'Failed to get response from Ollama',
          type: MessageType.error,
          timestamp: DateTime.now(),
          sessionId: _currentSessionId,
        );
        
        emit(state.copyWith(
          messages: [...state.messages, errorMessage],
          isProcessing: false,
        ));
      }
    } catch (e) {
      final errorMessage = Message(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        content: 'Error: ${e.toString()}',
        type: MessageType.error,
        timestamp: DateTime.now(),
        sessionId: _currentSessionId,
      );
      
      emit(state.copyWith(
        messages: [...state.messages, errorMessage],
        isProcessing: false,
      ));
    }
  }
  
  Future<void> _onSelectFollowUp(
    SelectFollowUpEvent event,
    Emitter<ChatState> emit,
  ) async {
    final state = this.state;
    if (state is! ChatLoaded) return;
    
    // Add follow-up as user message
    final followUpMessage = Message(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: event.choice.label,
      type: MessageType.followUp,
      timestamp: DateTime.now(),
      sessionId: _currentSessionId,
      metadata: {
        'action': event.choice.action.name,
        'questionId': event.question.id,
      },
    );
    
    emit(state.copyWith(
      messages: [...state.messages, followUpMessage],
      lastFollowUpQuestions: null,
    ));
    
    // Process the follow-up prompt
    add(SendMessageEvent(
      content: event.choice.prompt,
      sessionId: _currentSessionId,
    ));
  }
  
  Future<void> _onExtractIdea(
    ExtractIdeaEvent event,
    Emitter<ChatState> emit,
  ) async {
    final state = this.state;
    if (state is! ChatLoaded) return;
    
    // Find the message
    final message = state.messages.firstWhere(
      (m) => m.id == event.messageId,
      orElse: () => throw Exception('Message not found'),
    );
    
    // Extract key concepts as ideas
    final concepts = ResponseParser.extractKeyConcepts(message.content);
    
    for (final concept in concepts) {
      final idea = Idea(
        content: concept,
        type: IdeaType.concept,
        parentId: _currentIdea?.id,
        metadata: {
          'extractedFrom': message.id,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
      _ideaTree.addIdea(idea, parentId: _currentIdea?.id);
    }
    
    emit(state.copyWith(ideaTree: _ideaTree));
    
    // Save session after extracting ideas
    await _saveCurrentSession();
  }
  
  Future<void> _onLoadTemplate(
    LoadTemplateEvent event,
    Emitter<ChatState> emit,
  ) async {
    final state = this.state;
    if (state is! ChatLoaded) return;
    
    // Load template from repository
    final template = await _templateRepository.getTemplateById(event.templateId);
    
    if (template != null) {
      add(SendMessageEvent(
        content: template.prompt,
        sessionId: _currentSessionId,
      ));
    }
  }
  
  Future<void> _onExportIdeas(
    ExportIdeasEvent event,
    Emitter<ChatState> emit,
  ) async {
    final state = this.state;
    if (state is! ChatLoaded) return;

    try {
      // Create export data
      final exportData = ExportData(
        ideaTree: _ideaTree,
        exportedAt: DateTime.now(),
        sessionTitle: _currentSession?.title ?? 'Brainstorm Session',
        sessionId: _currentSessionId ?? 'unknown',
        messages: event.includeMessages ? state.messages : [],
        includeMessages: event.includeMessages,
        includeMetadata: event.includeMetadata,
        metadata: event.includeMetadata ? {
          'ideaCount': _ideaTree.allIdeas.length,
          'messageCount': state.messages.length,
          'sessionStatus': _currentSession?.status.name ?? 'active',
          'referenceFolderId': _referenceFolderId,
          'contextFilesCount': _contextFiles.length,
        } : {},
      );

      // Export data using the service
      await _exportService.exportData(
        exportData,
        event.format,
        customFileName: event.fileName.isNotEmpty ? event.fileName : null,
      );

      // Emit success state - could add this to the state if needed
      // For now, the UI will handle the success through the returned file
    } catch (e) {
      // Handle export error - could emit an error state if needed
      rethrow; // Let the UI handle the error
    }
  }

  
  Future<void> _saveCurrentSession() async {
    if (_currentSessionId == null) return;
    
    try {
      if (_currentSession == null) {
        // Create new session if it doesn't exist
        _currentSession = Session(
          id: _currentSessionId!,
          title: 'Brainstorm Session',
          createdAt: DateTime.now(),
          status: SessionStatus.active,
          ideaTree: _ideaTree,
        );
        await _sessionRepository.createSession(_currentSession!);
      } else {
        // Update existing session
        _currentSession = _currentSession!.copyWith(
          ideaTree: _ideaTree,
          lastModified: DateTime.now(),
        );
        await _sessionRepository.updateSession(_currentSession!);
      }
      
      // Cache the session if offline service is available
      if (_offlineService != null && _currentSession != null) {
        await _offlineService!.cacheSession(_currentSession!);
      }
    } catch (e) {
      // Log error but don't fail the operation
      // Log error but don't fail the operation
    }
  }

}