import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/claude_cli/mock_claude_process_manager.dart';
import '../../../../core/ollama_api/ollama_api_manager.dart';

class ClaudeTestPage extends StatefulWidget {
  const ClaudeTestPage({super.key});

  @override
  State<ClaudeTestPage> createState() => _ClaudeTestPageState();
}

class _ClaudeTestPageState extends State<ClaudeTestPage> {
  late dynamic _aiManager; // OllamaApiManager or MockClaudeProcessManager
  bool _isChecking = false;
  bool? _isAvailable;
  String? _version;
  String? _lastError;
  bool _useMock = false;
  
  final _promptController = TextEditingController();
  String? _lastResponse;
  bool _isSending = false;

  @override
  void initState() {
    super.initState();
    _aiManager = context.read<OllamaApiManager>();
    _checkOllamaAvailability();
  }

  @override
  void dispose() {
    _promptController.dispose();
    super.dispose();
  }

  Future<void> _checkOllamaAvailability() async {
    setState(() {
      _isChecking = true;
      _lastError = null;
    });

    try {
      final isAvailable = await _aiManager.isClaudeAvailable(); // Compatibility method
      String? version;
      
      if (isAvailable) {
        version = await _aiManager.getClaudeVersion(); // Compatibility method
      }

      setState(() {
        _isAvailable = isAvailable;
        _version = version;
        _isChecking = false;
      });
    } catch (e) {
      setState(() {
        _isAvailable = false;
        _lastError = e.toString();
        _isChecking = false;
      });
    }
  }

  Future<void> _sendTestPrompt() async {
    final prompt = _promptController.text.trim();
    if (prompt.isEmpty) return;

    setState(() {
      _isSending = true;
      _lastResponse = null;
      _lastError = null;
    });

    try {
      final response = await _aiManager.sendToClaudeCLI( // Compatibility method
        prompt: prompt,
        sessionId: 'test_${DateTime.now().millisecondsSinceEpoch}',
      );

      setState(() {
        if (response.success) {
          _lastResponse = response.content; // Updated to use 'content' property
        } else {
          _lastError = response.error;
        }
        _isSending = false;
      });
    } catch (e) {
      setState(() {
        _lastError = e.toString();
        _isSending = false;
      });
    }
  }

  void _toggleMockMode() {
    setState(() {
      _useMock = !_useMock;
      if (_useMock) {
        _aiManager = MockClaudeProcessManager();
      } else {
        _aiManager = context.read<OllamaApiManager>();
      }
      _isAvailable = null;
      _version = null;
      _lastResponse = null;
      _lastError = null;
    });
    _checkOllamaAvailability();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        title: const Text('Ollama AI Test'),
        leading: IconButton(
          icon: const Icon(LucideIcons.arrowLeft),
          onPressed: () => context.pop(),
        ),
        actions: [
          Switch(
            value: _useMock,
            onChanged: (_) => _toggleMockMode(),
          ),
          const SizedBox(width: 8),
          Text(_useMock ? 'Mock' : 'Live'),
          const SizedBox(width: 16),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Status Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          LucideIcons.terminal,
                          color: theme.colorScheme.primary,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Ollama Server Status',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const Spacer(),
                        if (_isChecking)
                          const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        else
                          IconButton(
                            icon: const Icon(LucideIcons.refreshCw, size: 20),
                            onPressed: _checkOllamaAvailability,
                          ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    _buildStatusRow(
                      'Available',
                      _isAvailable == null
                          ? 'Checking...'
                          : _isAvailable!
                              ? 'Yes'
                              : 'No',
                      _isAvailable == true
                          ? Colors.green
                          : _isAvailable == false
                              ? Colors.red
                              : null,
                    ),
                    const SizedBox(height: 8),
                    _buildStatusRow(
                      'Version',
                      _version ?? (_isAvailable == true ? 'Unknown' : 'N/A'),
                    ),
                    if (_useMock) ...[
                      const SizedBox(height: 8),
                      _buildStatusRow(
                        'Mode',
                        'Mock (Testing)',
                        Colors.orange,
                      ),
                    ],
                    if (_lastError != null && _lastResponse == null) ...[
                      const SizedBox(height: 12),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.errorContainer,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              LucideIcons.alertCircle,
                              size: 20,
                              color: theme.colorScheme.onErrorContainer,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _lastError!,
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onErrorContainer,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Test Prompt Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Test Prompt',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 12),
                    TextField(
                      controller: _promptController,
                      maxLines: 3,
                      decoration: InputDecoration(
                        hintText: 'Enter a test prompt...',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        filled: true,
                        fillColor: theme.colorScheme.surfaceContainerHighest,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: FilledButton.icon(
                            onPressed: (_isAvailable == true || _useMock) && !_isSending
                                ? _sendTestPrompt
                                : null,
                            icon: _isSending
                                ? const SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color: Colors.white,
                                    ),
                                  )
                                : const Icon(LucideIcons.send),
                            label: Text(_isSending ? 'Sending...' : 'Send Test'),
                          ),
                        ),
                        const SizedBox(width: 12),
                        OutlinedButton(
                          onPressed: () {
                            _promptController.text = 'Help me brainstorm creative ideas for a productivity mobile app';
                          },
                          child: const Text('Sample'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            // Response Section
            if (_lastResponse != null) ...[
              const SizedBox(height: 16),
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              LucideIcons.messageSquare,
                              color: theme.colorScheme.primary,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Response',
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Expanded(
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.surfaceContainerLow,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: SingleChildScrollView(
                              child: SelectableText(
                                _lastResponse!,
                                style: theme.textTheme.bodyMedium,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
            
            // Instructions
            if (_isAvailable == false && !_useMock) ...[
              const SizedBox(height: 16),
              Card(
                color: theme.colorScheme.primaryContainer,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            LucideIcons.info,
                            color: theme.colorScheme.onPrimaryContainer,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Ollama Server Not Found',
                            style: theme.textTheme.titleMedium?.copyWith(
                              color: theme.colorScheme.onPrimaryContainer,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Text(
                        'To use the Ollama integration:\n'
                        '1. Download Ollama from ollama.com\n'
                        '2. Install and run the Ollama application\n'
                        '3. Pull a model (e.g., "ollama pull gemma3:4b")\n'
                        '4. Ensure Ollama is running on localhost:11434\n\n'
                        'You can enable Mock mode to test the app without Ollama.',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onPrimaryContainer,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, String value, [Color? valueColor]) {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        Text(
          '$label:',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          value,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: valueColor ?? theme.colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }
}