import 'dart:io';
import 'package:flutter/material.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:url_launcher/url_launcher.dart';

class PlatformInstallationGuide extends StatelessWidget {
  const PlatformInstallationGuide({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final platform = _getCurrentPlatform();
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getPlatformIcon(platform),
                    color: theme.colorScheme.onPrimaryContainer,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Installation for ${_getPlatformName(platform)}',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        'Step-by-step instructions for your platform',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Platform-specific instructions
            ..._buildPlatformInstructions(context, platform),
            
            const SizedBox(height: 16),
            
            // Action buttons
            Row(
              children: [
                Expanded(
                  child: FilledButton.icon(
                    onPressed: _launchOllamaWebsite,
                    icon: const Icon(LucideIcons.externalLink),
                    label: const Text('Download Ollama'),
                    style: FilledButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: theme.colorScheme.onPrimary,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                OutlinedButton.icon(
                  onPressed: () => _showTroubleshootingDialog(context),
                  icon: const Icon(LucideIcons.helpCircle),
                  label: const Text('Help'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildPlatformInstructions(BuildContext context, _Platform platform) {
    switch (platform) {
      case _Platform.macOS:
        return _buildMacOSInstructions(context);
      case _Platform.windows:
        return _buildWindowsInstructions(context);
      case _Platform.linux:
        return _buildLinuxInstructions(context);
      case _Platform.unknown:
        return _buildGenericInstructions(context);
    }
  }

  List<Widget> _buildMacOSInstructions(BuildContext context) {
    return [
      _buildInstructionStep(
        context,
        1,
        'Download Ollama for macOS',
        'Visit ollama.com and download the macOS installer (supports both Intel and Apple Silicon)',
        LucideIcons.download,
      ),
      const SizedBox(height: 8),
      _buildInstructionStep(
        context,
        2,
        'Install the application',
        'Open the downloaded .dmg file and drag Ollama to Applications folder',
        LucideIcons.folder,
      ),
      const SizedBox(height: 8),
      _buildInstructionStep(
        context,
        3,
        'Run Ollama',
        'Launch Ollama from Applications - it will start automatically and add a menu bar icon',
        LucideIcons.play,
      ),
      const SizedBox(height: 8),
      _buildInstructionStep(
        context,
        4,
        'Install your first model',
        'Open Terminal and run: ollama pull gemma2:2b',
        LucideIcons.terminal,
        codeExample: 'ollama pull gemma2:2b',
      ),
    ];
  }

  List<Widget> _buildWindowsInstructions(BuildContext context) {
    return [
      _buildInstructionStep(
        context,
        1,
        'Download Ollama for Windows',
        'Visit ollama.com and download the Windows installer (.exe file)',
        LucideIcons.download,
      ),
      const SizedBox(height: 8),
      _buildInstructionStep(
        context,
        2,
        'Run the installer',
        'Right-click the installer and select "Run as administrator"',
        LucideIcons.shieldCheck,
      ),
      const SizedBox(height: 8),
      _buildInstructionStep(
        context,
        3,
        'Complete setup',
        'Follow the installation wizard - Ollama will start automatically after installation',
        LucideIcons.settings,
      ),
      const SizedBox(height: 8),
      _buildInstructionStep(
        context,
        4,
        'Install your first model',
        'Open Command Prompt or PowerShell and run: ollama pull gemma2:2b',
        LucideIcons.terminal,
        codeExample: 'ollama pull gemma2:2b',
      ),
    ];
  }

  List<Widget> _buildLinuxInstructions(BuildContext context) {
    return [
      _buildInstructionStep(
        context,
        1,
        'Install via curl',
        'Open terminal and run the installation script',
        LucideIcons.terminal,
        codeExample: 'curl -fsSL https://ollama.com/install.sh | sh',
      ),
      const SizedBox(height: 8),
      _buildInstructionStep(
        context,
        2,
        'Start Ollama service',
        'Start the Ollama service (usually starts automatically)',
        LucideIcons.play,
        codeExample: 'systemctl start ollama',
      ),
      const SizedBox(height: 8),
      _buildInstructionStep(
        context,
        3,
        'Install your first model',
        'Pull a model to get started with AI conversations',
        LucideIcons.download,
        codeExample: 'ollama pull gemma2:2b',
      ),
    ];
  }

  List<Widget> _buildGenericInstructions(BuildContext context) {
    return [
      _buildInstructionStep(
        context,
        1,
        'Visit ollama.com',
        'Go to the official Ollama website and download for your platform',
        LucideIcons.globe,
      ),
      const SizedBox(height: 8),
      _buildInstructionStep(
        context,
        2,
        'Install Ollama',
        'Follow the installation instructions for your operating system',
        LucideIcons.package,
      ),
      const SizedBox(height: 8),
      _buildInstructionStep(
        context,
        3,
        'Start the service',
        'Make sure Ollama is running on localhost:11434',
        LucideIcons.server,
      ),
      const SizedBox(height: 8),
      _buildInstructionStep(
        context,
        4,
        'Install a model',
        'Use the command line to pull your first model',
        LucideIcons.terminal,
        codeExample: 'ollama pull gemma2:2b',
      ),
    ];
  }

  Widget _buildInstructionStep(
    BuildContext context,
    int number,
    String title,
    String description,
    IconData icon, {
    String? codeExample,
  }) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerLow,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Text(
                number.toString(),
                style: theme.textTheme.labelSmall?.copyWith(
                  color: theme.colorScheme.onPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Icon(
            icon,
            color: theme.colorScheme.onSurfaceVariant,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                if (codeExample != null) ...[
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      codeExample,
                      style: theme.textTheme.bodySmall?.copyWith(
                        fontFamily: 'monospace',
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _launchOllamaWebsite() async {
    final Uri url = Uri.parse('https://ollama.com');
    if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
      debugPrint('Could not launch $url');
    }
  }

  void _showTroubleshootingDialog(BuildContext context) {
    final theme = Theme.of(context);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(LucideIcons.helpCircle),
            SizedBox(width: 8),
            Text('Troubleshooting'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Common Issues:',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),
              
              _buildTroubleshootingItem(
                context,
                'Ollama not starting',
                'Check if port 11434 is available and restart Ollama service',
              ),
              
              _buildTroubleshootingItem(
                context,
                'Model pull fails',
                'Ensure you have internet connection and sufficient disk space',
              ),
              
              _buildTroubleshootingItem(
                context,
                'Permission denied',
                'Run terminal/command prompt as administrator and try again',
              ),
              
              _buildTroubleshootingItem(
                context,
                'Out of memory',
                'Try smaller models like llama3.2:1b or close other applications',
              ),
              
              const SizedBox(height: 12),
              
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primaryContainer.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          LucideIcons.info,
                          color: theme.colorScheme.primary,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Need more help?',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: theme.colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Visit the official Ollama documentation at ollama.com/docs or check the GitHub repository for detailed troubleshooting guides.',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildTroubleshootingItem(BuildContext context, String title, String description) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '• $title',
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Padding(
            padding: const EdgeInsets.only(left: 12),
            child: Text(
              description,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ),
        ],
      ),
    );
  }

  _Platform _getCurrentPlatform() {
    if (Platform.isMacOS) return _Platform.macOS;
    if (Platform.isWindows) return _Platform.windows;
    if (Platform.isLinux) return _Platform.linux;
    return _Platform.unknown;
  }

  String _getPlatformName(_Platform platform) {
    switch (platform) {
      case _Platform.macOS:
        return 'macOS';
      case _Platform.windows:
        return 'Windows';
      case _Platform.linux:
        return 'Linux';
      case _Platform.unknown:
        return 'Your Platform';
    }
  }

  IconData _getPlatformIcon(_Platform platform) {
    switch (platform) {
      case _Platform.macOS:
        return LucideIcons.laptop;
      case _Platform.windows:
        return LucideIcons.monitor;
      case _Platform.linux:
        return LucideIcons.terminal;
      case _Platform.unknown:
        return LucideIcons.monitor;
    }
  }
}

enum _Platform { macOS, windows, linux, unknown }