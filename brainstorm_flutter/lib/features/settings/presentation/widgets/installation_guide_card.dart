import 'package:flutter/material.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:url_launcher/url_launcher.dart';

class InstallationGuideCard extends StatelessWidget {
  final VoidCallback? onInstallPressed;

  const InstallationGuideCard({
    super.key,
    this.onInstallPressed,
  });

  Future<void> _launchOllamaWebsite() async {
    final Uri url = Uri.parse('https://ollama.com');
    if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
      // Handle error - could show a snackbar or dialog
      debugPrint('Could not launch $url');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      color: theme.colorScheme.primaryContainer,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    LucideIcons.download,
                    color: theme.colorScheme.onPrimary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Install Ollama',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: theme.colorScheme.onPrimaryContainer,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        'Get started with local AI models on your device',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onPrimaryContainer.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Installation steps
            _buildStep(
              context,
              1,
              'Download Ollama',
              'Visit ollama.com and download for your platform',
              LucideIcons.globe,
            ),
            
            const SizedBox(height: 8),
            
            _buildStep(
              context,
              2,
              'Install the application',
              'Run the installer and follow the setup wizard',
              LucideIcons.package,
            ),
            
            const SizedBox(height: 8),
            
            _buildStep(
              context,
              3,
              'Pull your first model',
              'Run: ollama pull gemma2:2b',
              LucideIcons.terminal,
            ),
            
            const SizedBox(height: 16),
            
            // Action buttons
            Row(
              children: [
                Expanded(
                  child: FilledButton.icon(
                    onPressed: onInstallPressed ?? _launchOllamaWebsite,
                    icon: const Icon(LucideIcons.externalLink),
                    label: const Text('Open ollama.com'),
                    style: FilledButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: theme.colorScheme.onPrimary,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                OutlinedButton.icon(
                  onPressed: () => _showDetailedInstructions(context),
                  icon: const Icon(LucideIcons.info),
                  label: const Text('Details'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: theme.colorScheme.onPrimaryContainer,
                    side: BorderSide(color: theme.colorScheme.onPrimaryContainer.withOpacity(0.3)),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStep(BuildContext context, int number, String title, String description, IconData icon) {
    final theme = Theme.of(context);
    
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: theme.colorScheme.primary,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Center(
            child: Text(
              number.toString(),
              style: theme.textTheme.labelSmall?.copyWith(
                color: theme.colorScheme.onPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Icon(
          icon,
          color: theme.colorScheme.onPrimaryContainer,
          size: 16,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onPrimaryContainer,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                description,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onPrimaryContainer.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _showDetailedInstructions(BuildContext context) {
    final theme = Theme.of(context);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(LucideIcons.info),
            SizedBox(width: 8),
            Text('Ollama Installation Guide'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Detailed Installation Steps:',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),
              
              _buildDetailedStep(
                context,
                '1. System Requirements',
                '• macOS 11+ (Apple Silicon or Intel)\n• Windows 10+ with WSL2\n• Linux with Docker support\n• At least 8GB RAM recommended',
              ),
              
              _buildDetailedStep(
                context,
                '2. Download & Install',
                '• Go to https://ollama.com\n• Click "Download" for your OS\n• Run the installer as administrator\n• Restart your terminal/command prompt',
              ),
              
              _buildDetailedStep(
                context,
                '3. Verify Installation',
                '• Open terminal/command prompt\n• Run: ollama --version\n• You should see version information',
              ),
              
              _buildDetailedStep(
                context,
                '4. Get Your First Model',
                '• For fast responses: ollama pull gemma2:2b\n• For better quality: ollama pull llama3.2:7b\n• For creative tasks: ollama pull gemma2:2b',
              ),
              
              _buildDetailedStep(
                context,
                '5. Test the Setup',
                '• Run: ollama run gemma2:2b\n• Type a test message\n• Type /bye to exit\n• Return to this app and refresh models',
              ),
              
              const SizedBox(height: 12),
              
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primaryContainer.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          LucideIcons.lightbulb,
                          color: theme.colorScheme.primary,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Pro Tips:',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: theme.colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '• Models run locally - no internet needed after download\n• Larger models give better results but use more resources\n• You can have multiple models installed\n• Models are stored in ~/.ollama by default',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedStep(BuildContext context, String title, String content) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            content,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }
}