import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../../../../core/templates/models/template.dart';

class TemplateEditorDialog extends StatefulWidget {
  final Template? template;

  const TemplateEditorDialog({
    super.key,
    this.template,
  });

  @override
  State<TemplateEditorDialog> createState() => _TemplateEditorDialogState();
}

class _TemplateEditorDialogState extends State<TemplateEditorDialog> {
  late final TextEditingController _nameController;
  late final TextEditingController _descriptionController;
  late final TextEditingController _promptController;
  late final TextEditingController _followUpController;
  late String _selectedCategory;
  final List<String> _followUpPatterns = [];
  final _formKey = GlobalKey<FormState>();

  static const List<String> _categories = [
    'Creative',
    'Analytical',
    'Strategic',
    'Problem-Solving',
    'General',
  ];

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.template?.name ?? '');
    _descriptionController = TextEditingController(text: widget.template?.description ?? '');
    _promptController = TextEditingController(text: widget.template?.prompt ?? '');
    _followUpController = TextEditingController();
    _selectedCategory = widget.template?.metadata?['category'] ?? 'General';
    if (widget.template?.followUpPatterns != null) {
      _followUpPatterns.addAll(widget.template!.followUpPatterns!);
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _promptController.dispose();
    _followUpController.dispose();
    super.dispose();
  }

  void _addFollowUp() {
    final followUp = _followUpController.text.trim();
    if (followUp.isNotEmpty) {
      setState(() {
        _followUpPatterns.add(followUp);
        _followUpController.clear();
      });
    }
  }

  void _removeFollowUp(int index) {
    setState(() {
      _followUpPatterns.removeAt(index);
    });
  }

  void _save() {
    if (_formKey.currentState!.validate()) {
      final template = Template(
        id: widget.template?.id ?? const Uuid().v4(),
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        prompt: _promptController.text.trim(),
        followUpPatterns: _followUpPatterns.isEmpty ? null : List.from(_followUpPatterns),
        metadata: {
          'category': _selectedCategory,
          'createdAt': widget.template?.metadata?['createdAt'] ?? DateTime.now().toIso8601String(),
          'updatedAt': DateTime.now().toIso8601String(),
        },
      );

      Navigator.of(context).pop(template);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isEditing = widget.template != null;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(28),
      ),
      child: Container(
        width: 600,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.9,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.all(24),
              child: Row(
                children: [
                  Icon(
                    isEditing ? Icons.edit : Icons.add,
                    color: theme.colorScheme.primary,
                  ),
                  const SizedBox(width: 16),
                  Text(
                    isEditing ? 'Edit Template' : 'Create Template',
                    style: theme.textTheme.headlineSmall,
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
            ),
            Flexible(
              child: Form(
                key: _formKey,
                child: ListView(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  shrinkWrap: true,
                  children: [
                    TextFormField(
                      controller: _nameController,
                      decoration: const InputDecoration(
                        labelText: 'Template Name',
                        hintText: 'Enter a name for your template',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.label),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter a template name';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: _selectedCategory,
                      decoration: const InputDecoration(
                        labelText: 'Category',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.category),
                      ),
                      items: _categories.map((category) {
                        return DropdownMenuItem(
                          value: category,
                          child: Text(category),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _selectedCategory = value;
                          });
                        }
                      },
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _descriptionController,
                      decoration: const InputDecoration(
                        labelText: 'Description',
                        hintText: 'Describe what this template is for',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.description),
                      ),
                      maxLines: 2,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter a description';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _promptController,
                      decoration: const InputDecoration(
                        labelText: 'Prompt Template',
                        hintText: 'Enter the prompt template that will be sent to the AI',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.chat),
                      ),
                      maxLines: 5,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter a prompt template';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 24),
                    Text(
                      'Follow-up Patterns',
                      style: theme.textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _followUpController,
                            decoration: const InputDecoration(
                              hintText: 'Add a follow-up pattern',
                              border: OutlineInputBorder(),
                              isDense: true,
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 12,
                              ),
                            ),
                            onSubmitted: (_) => _addFollowUp(),
                          ),
                        ),
                        const SizedBox(width: 8),
                        IconButton(
                          icon: const Icon(Icons.add),
                          onPressed: _addFollowUp,
                          style: IconButton.styleFrom(
                            backgroundColor: theme.colorScheme.primary,
                            foregroundColor: theme.colorScheme.onPrimary,
                          ),
                        ),
                      ],
                    ),
                    if (_followUpPatterns.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      ...List.generate(_followUpPatterns.length, (index) {
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 4),
                          child: Chip(
                            label: Text(_followUpPatterns[index]),
                            onDeleted: () => _removeFollowUp(index),
                            deleteIconColor: theme.colorScheme.error,
                          ),
                        );
                      }),
                    ],
                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(24),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 8),
                  FilledButton(
                    onPressed: _save,
                    child: Text(isEditing ? 'Save Changes' : 'Create Template'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}