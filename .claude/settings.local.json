{"permissions": {"allow": ["mcp__serena__list_memories", "mcp__sequential-thinking__sequentialthinking", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(chmod:*)", "Bash(find /Users/<USER>/luminar-flutter/flutter_apps -name \"*.dart\")", "Bash(find /Users/<USER>/luminar-flutter/flutter_apps -type f -name \"*.dart\" -exec wc -l {} +)", "Bash(cd /Users/<USER>/luminar-flutter/flutter_apps/amna_flutter)", "<PERSON><PERSON>(flutter doctor --android-licenses)", "Bash(ls -la)", "<PERSON>sh(flutter analyze)", "Bash(find /Users/<USER>/luminar-flutter/flutter_apps -name \"android\" -type d)", "Bash(find /Users/<USER>/luminar-flutter/flutter_apps -name \"ios\" -type d)", "Bash(find /Users/<USER>/luminar-flutter/flutter_apps -maxdepth 3 -type d)", "Bash(cd /Users/<USER>/luminar-flutter/flutter_apps)", "Bash(flutter create --platforms=android,ios,web amna_flutter_new)", "Bash(cd /Users/<USER>/luminar-flutter/flutter_apps/amna_flutter_new)", "Bash(flutter pub get)", "Bash(find:*)", "Bash(cp:*)", "Bash(flutter build:*)", "<PERSON><PERSON>(flutter run:*)", "<PERSON><PERSON>(flutter test:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(python3:*)", "Bash(flutter analyze:*)", "mcp__filesystem__list_directory", "Bash(flutter create:*)", "<PERSON><PERSON>(touch:*)", "Bash(grep:*)", "<PERSON><PERSON>(dart:*)", "Bash(rm:*)", "<PERSON><PERSON>(flutter clean:*)", "mcp__serena__find_file", "mcp__serena__read_memory", "mcp__serena__search_for_pattern", "mcp__serena__get_symbols_overview", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "mcp__fetch__fetch", "<PERSON><PERSON>(flutter doctor:*)", "Bash(flutter pub:*)", "Bash(ls:*)", "mcp__claude-task-master__get_tasks"], "deny": []}}